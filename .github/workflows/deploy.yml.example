# Curio API Deployment Workflow Example
# Copy this file to .github/workflows/deploy.yml and configure your secrets

name: Deploy Curio API

on:
  push:
    branches: [main]
  workflow_dispatch:

env:
  DOTNET_VERSION: '8.0.x'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Test
      run: dotnet test --no-build --configuration Release --verbosity normal

    - name: Publish API
      run: dotnet publish src/Curio.Api/Curio.Api.csproj -c Release -o ./publish/api

    - name: Publish Silo
      run: dotnet publish src/Curio.Orleans.Silo/Curio.Orleans.Silo.csproj -c Release -o ./publish/silo

    - name: Upload API artifacts
      uses: actions/upload-artifact@v4
      with:
        name: curio-api
        path: ./publish/api

    - name: Upload Silo artifacts
      uses: actions/upload-artifact@v4
      with:
        name: curio-silo
        path: ./publish/silo

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    environment: staging
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Download API artifacts
      uses: actions/download-artifact@v4
      with:
        name: curio-api
        path: ./api

    - name: Download Silo artifacts
      uses: actions/download-artifact@v4
      with:
        name: curio-silo
        path: ./silo

    - name: Deploy to Staging
      run: |
        echo "🚀 Deploying to staging environment..."
        # Add your deployment commands here
        # Example: Deploy to Azure, AWS, or your server
      env:
        # Configure these secrets in GitHub repository settings
        DATABASE_PASSWORD: ${{ secrets.STAGING_DATABASE_PASSWORD }}
        JWT_SECRET_KEY: ${{ secrets.STAGING_JWT_SECRET_KEY }}
        ENCRYPTION_KEY: ${{ secrets.STAGING_ENCRYPTION_KEY }}
        ENCRYPTION_SALT: ${{ secrets.STAGING_ENCRYPTION_SALT }}
        SMTP_PASSWORD: ${{ secrets.STAGING_SMTP_PASSWORD }}
        KAFKA_SASL_PASSWORD: ${{ secrets.STAGING_KAFKA_SASL_PASSWORD }}

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Download API artifacts
      uses: actions/download-artifact@v4
      with:
        name: curio-api
        path: ./api

    - name: Download Silo artifacts
      uses: actions/download-artifact@v4
      with:
        name: curio-silo
        path: ./silo

    - name: Deploy to Production
      run: |
        echo "🚀 Deploying to production environment..."
        # Add your production deployment commands here

        # Example: Docker deployment
        # docker build -t curio-api:${{ github.sha }} .
        # docker push your-registry/curio-api:${{ github.sha }}
        # kubectl set image deployment/curio-api curio-api=your-registry/curio-api:${{ github.sha }}

        # Example: Direct server deployment
        # scp -r ./api user@server:/opt/curio-api/
        # ssh user@server "systemctl restart curio-api"
      env:
        # Configure these secrets in GitHub repository settings
        DATABASE_PASSWORD: ${{ secrets.PROD_DATABASE_PASSWORD }}
        JWT_SECRET_KEY: ${{ secrets.PROD_JWT_SECRET_KEY }}
        ENCRYPTION_KEY: ${{ secrets.PROD_ENCRYPTION_KEY }}
        ENCRYPTION_SALT: ${{ secrets.PROD_ENCRYPTION_SALT }}
        SMTP_PASSWORD: ${{ secrets.PROD_SMTP_PASSWORD }}
        KAFKA_SASL_PASSWORD: ${{ secrets.PROD_KAFKA_SASL_PASSWORD }}

        # Database configuration
        DATABASE_HOST: ${{ secrets.PROD_DATABASE_HOST }}
        DATABASE_PORT: ${{ secrets.PROD_DATABASE_PORT }}
        DATABASE_NAME: ${{ secrets.PROD_DATABASE_NAME }}
        DATABASE_USERNAME: ${{ secrets.PROD_DATABASE_USERNAME }}

        # Kafka configuration
        KAFKA_BROKERS: ${{ secrets.PROD_KAFKA_BROKERS }}
        KAFKA_CONSUMER_GROUP_ID: ${{ secrets.PROD_KAFKA_CONSUMER_GROUP_ID }}
        KAFKA_SECURITY_PROTOCOL: ${{ secrets.PROD_KAFKA_SECURITY_PROTOCOL }}
        KAFKA_SASL_USERNAME: ${{ secrets.PROD_KAFKA_SASL_USERNAME }}

        # Orleans configuration
        ORLEANS_CLUSTER_ID: ${{ secrets.PROD_ORLEANS_CLUSTER_ID }}
        ORLEANS_SERVICE_ID: ${{ secrets.PROD_ORLEANS_SERVICE_ID }}

        # Email configuration
        SMTP_HOST: ${{ secrets.PROD_SMTP_HOST }}
        SMTP_PORT: ${{ secrets.PROD_SMTP_PORT }}
        SMTP_USERNAME: ${{ secrets.PROD_SMTP_USERNAME }}
        EMAIL_FROM_ADDRESS: ${{ secrets.PROD_EMAIL_FROM_ADDRESS }}
        EMAIL_FROM_NAME: ${{ secrets.PROD_EMAIL_FROM_NAME }}

# Required GitHub Secrets:
#
# Staging Environment:
# - STAGING_DATABASE_PASSWORD
# - STAGING_JWT_SECRET_KEY
# - STAGING_ENCRYPTION_KEY
# - STAGING_ENCRYPTION_SALT
# - STAGING_SMTP_PASSWORD
# - STAGING_KAFKA_SASL_PASSWORD
#
# Production Environment:
# - PROD_DATABASE_PASSWORD
# - PROD_JWT_SECRET_KEY
# - PROD_ENCRYPTION_KEY
# - PROD_ENCRYPTION_SALT
# - PROD_SMTP_PASSWORD
# - PROD_KAFKA_SASL_PASSWORD
# - PROD_DATABASE_HOST
# - PROD_DATABASE_PORT
# - PROD_DATABASE_NAME
# - PROD_DATABASE_USERNAME
# - PROD_KAFKA_BROKERS
# - PROD_KAFKA_CONSUMER_GROUP_ID
# - PROD_KAFKA_SECURITY_PROTOCOL
# - PROD_KAFKA_SASL_USERNAME
# - PROD_ORLEANS_CLUSTER_ID
# - PROD_ORLEANS_SERVICE_ID
# - PROD_SMTP_HOST
# - PROD_SMTP_PORT
# - PROD_SMTP_USERNAME
# - PROD_EMAIL_FROM_ADDRESS
# - PROD_EMAIL_FROM_NAME
