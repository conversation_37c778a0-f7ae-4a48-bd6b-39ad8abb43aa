name: Deploy Curio API

on:
  push:
    branches: [main, develop]
  workflow_dispatch:

env:
  DOTNET_VERSION: '8.0.x'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}

    - name: Restore dependencies
      run: dotnet restore

    - name: Build
      run: dotnet build --no-restore --configuration Release

    - name: Test
      run: dotnet test --no-build --configuration Release --verbosity normal

    - name: Publish API
      run: dotnet publish src/Curio.Api/Curio.Api.csproj -c Release -o ./publish/api

    - name: Publish Silo
      run: dotnet publish src/Curio.Orleans.Silo/Curio.Orleans.Silo.csproj -c Release -o ./publish/silo

    - name: Upload API artifacts
      uses: actions/upload-artifact@v4
      with:
        name: curio-api-${{ github.sha }}
        path: ./publish/api

    - name: Upload Silo artifacts
      uses: actions/upload-artifact@v4
      with:
        name: curio-silo-${{ github.sha }}
        path: ./publish/silo

  deploy-development:
    needs: build
    runs-on: ubuntu-latest
    environment: development
    if: github.ref == 'refs/heads/develop'

    steps:
    - name: Download API artifacts
      uses: actions/download-artifact@v4
      with:
        name: curio-api-${{ github.sha }}
        path: ./api

    - name: Download Silo artifacts
      uses: actions/download-artifact@v4
      with:
        name: curio-silo-${{ github.sha }}
        path: ./silo

    - name: Deploy to Development
      run: |
        echo "🚀 Deploying to development environment..."
        echo "Branch: ${{ github.ref }}"
        echo "Commit: ${{ github.sha }}"

        # Add your development deployment commands here
        # Example: Deploy to development server or staging environment

      env:
        # Environment variables from GitHub Environment secrets
        ASPNETCORE_ENVIRONMENT: Development
        DATABASE__PASSWORD: ${{ secrets.DATABASE__PASSWORD }}
        APPLICATION__SECURITY__JWT__SECRETKEY: ${{ secrets.APPLICATION__SECURITY__JWT__SECRETKEY }}
        APPLICATION__SECURITY__ENCRYPTION__KEY: ${{ secrets.APPLICATION__SECURITY__ENCRYPTION__KEY }}
        APPLICATION__SECURITY__ENCRYPTION__SALT: ${{ secrets.APPLICATION__SECURITY__ENCRYPTION__SALT }}
        EMAIL__SMTP__USERNAME: ${{ secrets.EMAIL__SMTP__USERNAME }}
        EMAIL__SMTP__PASSWORD: ${{ secrets.EMAIL__SMTP__PASSWORD }}
        EMAIL__DEFAULTSENDER__FROMEMAIL: ${{ secrets.EMAIL__DEFAULTSENDER__FROMEMAIL }}
        KAFKA__SASLUSERNAME: ${{ secrets.KAFKA__SASLUSERNAME }}
        KAFKA__SASLPASSWORD: ${{ secrets.KAFKA__SASLPASSWORD }}

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Download API artifacts
      uses: actions/download-artifact@v4
      with:
        name: curio-api-${{ github.sha }}
        path: ./api

    - name: Download Silo artifacts
      uses: actions/download-artifact@v4
      with:
        name: curio-silo-${{ github.sha }}
        path: ./silo

    - name: Deploy to Production
      run: |
        echo "🚀 Deploying to production environment..."
        echo "Branch: ${{ github.ref }}"
        echo "Commit: ${{ github.sha }}"

        # Add your production deployment commands here
        # Examples:

        # Docker deployment:
        # docker build -t curio-api:${{ github.sha }} .
        # docker push your-registry/curio-api:${{ github.sha }}

        # Kubernetes deployment:
        # kubectl set image deployment/curio-api curio-api=your-registry/curio-api:${{ github.sha }}
        # kubectl set image deployment/curio-silo curio-silo=your-registry/curio-silo:${{ github.sha }}

        # Direct server deployment:
        # scp -r ./api user@server:/opt/curio-api/
        # scp -r ./silo user@server:/opt/curio-silo/
        # ssh user@server "systemctl restart curio-api curio-silo"

      env:
        # Environment variables from GitHub Environment secrets
        ASPNETCORE_ENVIRONMENT: Production
        DATABASE__PASSWORD: ${{ secrets.DATABASE__PASSWORD }}
        APPLICATION__SECURITY__JWT__SECRETKEY: ${{ secrets.APPLICATION__SECURITY__JWT__SECRETKEY }}
        APPLICATION__SECURITY__ENCRYPTION__KEY: ${{ secrets.APPLICATION__SECURITY__ENCRYPTION__KEY }}
        APPLICATION__SECURITY__ENCRYPTION__SALT: ${{ secrets.APPLICATION__SECURITY__ENCRYPTION__SALT }}
        EMAIL__SMTP__USERNAME: ${{ secrets.EMAIL__SMTP__USERNAME }}
        EMAIL__SMTP__PASSWORD: ${{ secrets.EMAIL__SMTP__PASSWORD }}
        EMAIL__DEFAULTSENDER__FROMEMAIL: ${{ secrets.EMAIL__DEFAULTSENDER__FROMEMAIL }}
        KAFKA__SASLUSERNAME: ${{ secrets.KAFKA__SASLUSERNAME }}
        KAFKA__SASLPASSWORD: ${{ secrets.KAFKA__SASLPASSWORD }}

        # Optional: Override non-sensitive configuration for production
        APPLICATION__API__BASEURL: ${{ vars.APPLICATION__API__BASEURL || 'https://api.yourcompany.com' }}
        DATABASE__HOST: ${{ vars.DATABASE__HOST || 'your-prod-db-host' }}
        DATABASE__PORT: ${{ vars.DATABASE__PORT || '5432' }}
        DATABASE__DATABASE: ${{ vars.DATABASE__DATABASE || 'curio' }}
        DATABASE__USERNAME: ${{ vars.DATABASE__USERNAME || 'curio' }}
        KAFKA__BROKERLIST__0: ${{ vars.KAFKA__BROKERLIST__0 || 'your-kafka-broker:9092' }}
        ORLEANS__CLUSTERID: ${{ vars.ORLEANS__CLUSTERID || 'curio-cluster-prod' }}
        ORLEANS__SERVICEID: ${{ vars.ORLEANS__SERVICEID || 'curio-service-prod' }}

# Required GitHub Environment Secrets:
#
# Development Environment (for develop branch):
# - DATABASE__PASSWORD
# - APPLICATION__SECURITY__JWT__SECRETKEY
# - APPLICATION__SECURITY__ENCRYPTION__KEY
# - APPLICATION__SECURITY__ENCRYPTION__SALT
# - EMAIL__SMTP__USERNAME (optional)
# - EMAIL__SMTP__PASSWORD (optional)
# - EMAIL__DEFAULTSENDER__FROMEMAIL (optional)
# - KAFKA__SASLUSERNAME (optional)
# - KAFKA__SASLPASSWORD (optional)
#
# Production Environment (for main branch):
# - DATABASE__PASSWORD
# - APPLICATION__SECURITY__JWT__SECRETKEY
# - APPLICATION__SECURITY__ENCRYPTION__KEY
# - APPLICATION__SECURITY__ENCRYPTION__SALT
# - EMAIL__SMTP__USERNAME (optional)
# - EMAIL__SMTP__PASSWORD (optional)
# - EMAIL__DEFAULTSENDER__FROMEMAIL (optional)
# - KAFKA__SASLUSERNAME (optional)
# - KAFKA__SASLPASSWORD (optional)
#
# Optional GitHub Environment Variables (non-sensitive):
# - APPLICATION__API__BASEURL
# - DATABASE__HOST
# - DATABASE__PORT
# - DATABASE__DATABASE
# - DATABASE__USERNAME
# - KAFKA__BROKERLIST__0
# - ORLEANS__CLUSTERID
# - ORLEANS__SERVICEID
