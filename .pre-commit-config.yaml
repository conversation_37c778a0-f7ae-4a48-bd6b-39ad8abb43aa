# Pre-commit hooks configuration for .NET project
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  # General file checks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        exclude: '\.md$'
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-added-large-files
        args: ["--maxkb=1000"]
      - id: mixed-line-ending
        args: ["--fix=lf"]

  # .NET specific hooks
  - repo: local
    hooks:
      - id: dotnet-format
        name: dotnet format
        entry: dotnet format --verify-no-changes
        language: system
        pass_filenames: false
        files: '\.(cs|csproj|sln)$'

      - id: dotnet-build
        name: dotnet build
        entry: dotnet build --no-restore
        language: system
        pass_filenames: false
        files: '\.(cs|csproj|sln)$'

      - id: dotnet-test
        name: dotnet test
        entry: dotnet test --no-build --no-restore
        language: system
        pass_filenames: false
        files: '\.(cs|csproj)$'

# Configuration for specific hooks
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ""
  autoupdate_commit_msg: "[pre-commit.ci] pre-commit autoupdate"
  autoupdate_schedule: weekly
  skip: [dotnet-test] # Skip tests in CI to speed up commits
  submodules: false
