# SMTP 邮件发送服务配置指南

## 📧 邮件系统架构

这个邮件系统采用了现代化的模板化设计，支持：

- **SMTP 发送**: 支持各种 SMTP 服务商（Gmail、Outlook、阿里云邮箱等）
- **模板引擎**: 基于 Handlebars.Net 的高性能模板系统
- **多格式支持**: 同时支持 HTML 和纯文本邮件
- **缓存优化**: 模板编译缓存，提升性能
- **重试机制**: 自动重试，支持指数退避
- **可扩展性**: 易于添加新的邮件模板

## 🛠️ SMTP 配置

### 1. 更新配置文件

在 `appsettings.json` 或 `appsettings.Development.json` 中填入您的 SMTP 信息：

```json
{
  "Email": {
    "Smtp": {
      "Host": "smtp.gmail.com",
      "Port": 587,
      "Username": "<EMAIL>",
      "Password": "your-app-password",
      "EnableSsl": true,
      "TimeoutSeconds": 30,
      "UseDefaultCredentials": false
    },
    "DefaultSender": {
      "FromEmail": "<EMAIL>", 
      "FromName": "Curio",
      "ReplyToEmail": "<EMAIL>",
      "ReplyToName": "Curio Support"
    }
  }
}
```

### 2. 常见 SMTP 服务商配置

#### Gmail
- Host: `smtp.gmail.com`
- Port: `587`
- SSL: `true`
- **重要**: 需要启用"两步验证"并生成"应用专用密码"

#### Outlook/Hotmail
- Host: `smtp-mail.outlook.com`
- Port: `587`
- SSL: `true`

#### 阿里云企业邮箱
- Host: `smtp.mxhichina.com`
- Port: `465`
- SSL: `true`

#### 腾讯企业邮箱
- Host: `smtp.exmail.qq.com`
- Port: `587`
- SSL: `true`

## 📝 邮件模板系统

### 模板位置
邮件模板存放在：`src/Curio.Api/EmailTemplates/`

### 模板格式
每个模板都支持两种格式：
- `template-name.html` - HTML 格式邮件
- `template-name.txt` - 纯文本格式邮件（可选）

### Handlebars 语法示例

```html
<!-- 基础变量 -->
<p>您好，{{name}}！</p>

<!-- 条件判断 -->
{{#ifEquals purpose "registration"}}
  <h1>欢迎注册！</h1>
{{else}}
  <h1>验证码登录</h1>
{{/ifEquals}}

<!-- 日期格式化 -->
<p>过期时间：{{formatDate expiresAt "yyyy-MM-dd HH:mm:ss"}}</p>

<!-- URL 编码 -->
<a href="https://example.com?token={{urlEncode token}}">点击验证</a>

<!-- 循环遍历 -->
{{#eachWithIndex items}}
  <li>{{index}}. {{this.name}}</li>
{{/eachWithIndex}}
```

### 可用的 Helper 函数

- `formatDate` - 日期格式化
- `formatCurrency` - 货币格式化
- `urlEncode` - URL 编码
- `ifEquals` - 条件比较
- `eachWithIndex` - 带索引的循环

## 🚀 使用示例

### 1. 发送模板化邮件

```csharp
// 注入服务
public class SomeService
{
    private readonly ISmtpEmailService _emailService;
    
    public SomeService(ISmtpEmailService emailService)
    {
        _emailService = emailService;
    }
    
    public async Task SendWelcomeEmailAsync(string email, string name)
    {
        var templateModel = new
        {
            name = name,
            email = email,
            registrationDate = DateTime.Now,
            supportEmail = "<EMAIL>"
        };
        
        await _emailService.SendTemplatedEmailAsync(
            "welcome-email", 
            email, 
            "欢迎加入 Curio！", 
            templateModel
        );
    }
}
```

### 2. 发送自定义邮件

```csharp
var message = new EmailMessage
{
    To = "<EMAIL>",
    ToName = "用户名",
    Subject = "重要通知",
    HtmlBody = "<h1>这是 HTML 邮件</h1><p>内容...</p>",
    PlainTextBody = "这是纯文本邮件\\n内容...",
    Headers = new Dictionary<string, string>
    {
        ["X-Priority"] = "1" // 高优先级
    }
};

await _emailService.SendEmailAsync(message);
```

### 3. 批量发送邮件

```csharp
var messages = users.Select(user => new EmailMessage
{
    To = user.Email,
    ToName = user.Name,
    Subject = "批量通知",
    HtmlBody = $"<p>您好 {user.Name}，这是批量邮件</p>"
});

await _emailService.SendBulkEmailAsync(messages);
```

## 🔧 高级配置

### 模板缓存配置

```json
{
  "Email": {
    "Templates": {
      "TemplatesDirectory": "EmailTemplates",
      "EnableCaching": true,
      "CacheExpirationMinutes": 60
    }
  }
}
```

### 重试策略配置

```json
{
  "Email": {
    "Retry": {
      "MaxAttempts": 3,
      "DelayMilliseconds": 1000,
      "ExponentialBackoff": true
    }
  }
}
```

## 📊 监控和日志

邮件发送会产生详细的日志：

```json
{
  "Logging": {
    "LogLevel": {
      "Curio.Infrastructure.Services": "Debug"
    }
  }
}
```

日志包含：
- 邮件发送成功/失败状态
- 重试次数和延迟
- 模板渲染时间
- SMTP 连接状态

## 🔒 安全注意事项

1. **密码安全**: 
   - 生产环境使用环境变量或密钥管理服务
   - 不要在代码中硬编码密码

2. **应用专用密码**: 
   - Gmail 等服务需要生成应用专用密码
   - 不要使用账户主密码

3. **SSL/TLS**: 
   - 始终启用 SSL 加密
   - 使用安全端口（587、465）

## 🧪 测试邮件发送

可以创建一个测试端点来验证邮件功能：

```csharp
[HttpPost("test-email")]
public async Task<IActionResult> TestEmail([FromBody] TestEmailRequest request)
{
    var templateModel = new
    {
        code = "123456",
        purpose = "registration",
        expiresAt = DateTime.UtcNow.AddMinutes(10),
        email = request.Email
    };
    
    var result = await _emailService.SendTemplatedEmailAsync(
        "verification-code", 
        request.Email, 
        "测试验证码", 
        templateModel
    );
    
    return Ok(new { success = result });
}
```

配置完成后，系统就可以发送专业的模板化邮件了！🎉
