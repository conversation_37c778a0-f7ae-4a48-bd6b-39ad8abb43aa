# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Curio API is a modern .NET backend service built with Orleans, Event Sourcing, CQRS, and Domain-Driven Design (DDD) principles. The project implements a distributed architecture using Kafka as the sole event storage system and Orleans for actor-based computation.

## Architecture Principles

### Core Design Patterns
- **Orleans Actor Model**: All business logic runs in Orleans Grains with distributed state management
- **Event Sourcing with Kafka**: Events are stored exclusively in Kafka topics, not in traditional databases
- **CQRS**: Complete separation of command (write) and query (read) operations
- **Domain-Driven Design**: Rich domain models with business logic encapsulated in aggregates

### Critical Architecture Decisions
- **Kafka as Sole Event Store**: Unlike traditional dual-write patterns, ALL domain events are persisted only in Kafka via Orleans Streams
- **PostgreSQL Role**: Used exclusively for Orleans clustering, Grain state snapshots, and read model projections - NOT for event storage
- **JournaledGrain Pattern**: All event-sourced Grains inherit from `JournaledGrain<TState, TEvent>` for automatic event handling

## Project Structure

Currently, the project contains:
- `/docs/` - Comprehensive architecture and design documentation
- `/deploy/` - Infrastructure deployment configurations (Kafka, PostgreSQL)

Expected structure (per architecture docs):
```
src/
├── Curio.Api/              # Web API controllers and SignalR hubs
├── Curio.Application/       # CQRS command/query handlers and application services  
├── Curio.Domain/            # Domain entities, aggregates, value objects, events
├── Curio.Infrastructure/    # External integrations and persistence implementations
├── Curio.Orleans.Interfaces/ # Grain interface definitions
├── Curio.Orleans.Grains/    # Grain implementations (domain logic)
├── Curio.Orleans.Silo/      # Orleans host configuration
└── Curio.Shared/            # Cross-cutting DTOs, events, contracts (carefully governed)
```

## Development Environment

### Infrastructure Commands

Start Kafka (required for event storage):
```bash
cd deploy/kafka
docker-compose up -d
```

Start PostgreSQL (Orleans clustering and read models):
```bash  
cd deploy/postgresql
docker-compose up -d
```

Access Kafka UI: http://localhost:9093 (username/password not required)
Access PostgreSQL: localhost:5432 (orleans/orleans123)

### Key Infrastructure Details
- **Kafka Topics**: `domain-events`, `projection-rebuild`, `integration-events`
- **PostgreSQL Databases**: Orleans clustering tables, Grain state storage, read model tables
- **Event Retention**: Kafka configured for 7-day retention with event sourcing optimization

## API Design Standards

### Response Format
ALL API responses use this exact format:
```json
{
  "success": boolean,
  "code": number,     // 5-digit business code (e.g., 20001, 40404)
  "message": string,
  "data": object|null
}
```

### Error Code Pattern
- Success: 2xxxx (e.g., 20001 for created, 20002 for updated)
- Client errors: 4xxxx (e.g., 40001 for validation, 40401 for not found)
- Server errors: 5xxxx (e.g., 50001 for internal error)

### Key Design Rules
- Use JournaledGrain for all event-sourced business logic
- Events published via Orleans Streams automatically persist to Kafka
- Implement idempotency using `Idempotency-Key` headers
- Follow DDD aggregate boundaries when designing Grains

## Curio.Shared Governance

**Critical**: The Curio.Shared project has strict governance rules to prevent architectural violations:

### Allowed Content
- DTOs (pure data containers with only properties)
- Domain events (cross-boundary contracts)
- Enums and constants
- Grain interfaces (Orleans contracts)
- Pure extension methods (stateless utilities)

### Prohibited Content  
- Business logic (belongs in Domain layer)
- Infrastructure concerns (belongs in Infrastructure layer)
- Configuration classes (belongs in Infrastructure layer)
- Validation logic (belongs in Domain/Application layers)
- API-specific helpers (belongs in API layer)

## Orleans + Event Sourcing Implementation

### Grain Development Pattern
```csharp
public class OrderGrain : JournaledGrain<OrderState, DomainEvent>, IOrderGrain
{
    public async Task<OrderDto> CreateOrderAsync(CreateOrderCommand command)
    {
        // 1. Check idempotency
        if (State.ProcessedCommands.Contains(command.CommandId))
            return State.ToDto();
            
        // 2. Business logic - generate events
        var events = State.CreateOrder(command);
        
        // 3. Raise events (auto-persisted to Kafka via Orleans Streams)
        foreach (var evt in events)
            RaiseEvent(evt);
            
        // 4. Confirm persistence
        await ConfirmEvents();
        
        return State.ToDto();
    }
}
```

### Event Flow Architecture
```
Orleans Grain → Orleans Streams → Kafka Topics → Event Handlers → Read Model Updates
```

## Important Notes

- **No Traditional Event Store**: Do not create separate event storage tables in PostgreSQL
- **State vs Events**: PostgreSQL Orleans tables store Grain state snapshots, not event history
- **Event Sourcing**: Complete event history lives in Kafka, state is rebuilt from events
- **Projection Rebuilding**: Use Kafka's timestamp-based replay for projection reconstruction
- **External Integration**: Other services consume events directly from Kafka topics

This architecture provides strong consistency, high performance, and natural scalability through Orleans' distributed actor model combined with Kafka's event streaming capabilities.
