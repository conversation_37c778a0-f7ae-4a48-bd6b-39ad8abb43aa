#!/bin/bash

# Setup script for pre-commit hooks
# This script installs pre-commit and sets up the hooks for the project

set -e

echo "🚀 Setting up pre-commit hooks for Curio API project..."

# Check if Python is installed
# if ! command -v python3 &> /dev/null; then
#     echo "❌ Python 3 is required but not installed. Please install Python 3 first."
#     exit 1
# fi

# Check if pip is installed
# if ! command -v pip3 &> /dev/null; then
#     echo "❌ pip3 is required but not installed. Please install pip3 first."
#     exit 1
# fi

# Install pre-commit if not already installed
if ! command -v pre-commit &> /dev/null; then
    echo "📦 Installing pre-commit..."
    pip3 install pre-commit
else
    echo "✅ pre-commit is already installed"
fi

# Check if .NET is installed
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET SDK is required but not installed. Please install .NET SDK first."
    exit 1
fi

# Install .NET tools
echo "🔧 Installing .NET tools..."
dotnet tool install -g dotnet-format || echo "dotnet-format already installed"



# Install pre-commit hooks
echo "🪝 Installing pre-commit hooks..."
pre-commit install

# Run pre-commit on all files to test setup
echo "🧪 Testing pre-commit setup..."
pre-commit run --all-files || echo "⚠️  Some hooks failed. This is normal for the first run."



echo ""
echo "✅ Pre-commit setup complete!"
echo ""
echo "📋 What was installed:"
echo "   • pre-commit hooks"
echo "   • dotnet-format"
echo ""
echo "🎯 Next steps:"
echo "   • Hooks will now run automatically on git commit"
echo "   • Run 'pre-commit run --all-files' to check all files"
echo "   • Run 'pre-commit autoupdate' to update hook versions"
echo ""
echo "🔧 Manual run commands:"
echo "   • Format code: dotnet format"
echo "   • Build solution: dotnet build"
echo "   • Run tests: dotnet test"
echo ""
