#!/bin/bash

# Curio API Configuration Test Script
# 用于测试配置文件加载是否正常

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_ROOT"

echo "🧪 Curio API Configuration Test"
echo "==============================="

# 检查必要的配置文件
echo "📋 Checking configuration files..."

check_file() {
    local file="$1"
    local required="$2"

    if [ -f "$file" ]; then
        echo "  ✅ $file (exists)"
        return 0
    else
        if [ "$required" = "true" ]; then
            echo "  ❌ $file (missing - required)"
            return 1
        else
            echo "  ⚠️  $file (missing - optional)"
            return 0
        fi
    fi
}

# 检查基础配置文件
check_file "src/Curio.Api/appsettings.json" "true" || exit 1
check_file "src/Curio.Api/appsettings.Development.json" "true" || exit 1
check_file "src/Curio.Api/appsettings.Production.json" "true" || exit 1
check_file "appsettings.template.json" "true" || exit 1

# 检查本地配置文件
if ! check_file "src/Curio.Api/appsettings.Local.json" "false"; then
    echo ""
    echo "💡 Tip: Create appsettings.Local.json for personal configuration:"
    echo "   cp appsettings.template.json src/Curio.Api/appsettings.Local.json"
    echo "   cp appsettings.template.json src/Curio.Orleans.Silo/appsettings.Local.json"
fi

echo ""

# 测试不同环境的配置加载
test_environment() {
    local env="$1"
    echo "🔍 Testing $env environment configuration..."

    # 设置环境变量
    export ASPNETCORE_ENVIRONMENT="$env"

    # 构建项目（如果需要）
    echo "  Building project..."
    dotnet build src/Curio.Api --configuration Release --verbosity quiet > /dev/null 2>&1

    if [ $? -eq 0 ]; then
        echo "  ✅ Build successful"
    else
        echo "  ❌ Build failed"
        return 1
    fi

    # 测试配置加载（运行应用几秒钟然后停止）
    echo "  Testing configuration loading..."

    # 使用 timeout 命令运行应用 5 秒钟
    timeout 5s dotnet run --project src/Curio.Api --configuration Release > /tmp/curio-test-$env.log 2>&1 || true

    # 检查日志中是否有配置摘要
    if grep -q "Configuration Summary" /tmp/curio-test-$env.log; then
        echo "  ✅ Configuration loaded successfully"

        # 显示配置摘要的关键部分
        echo "  📄 Configuration summary:"
        grep -A 10 "Configuration Summary" /tmp/curio-test-$env.log | sed 's/^/    /'
    else
        echo "  ⚠️  Configuration summary not found (app may not have started fully)"
        echo "  📄 Application output:"
        head -20 /tmp/curio-test-$env.log | sed 's/^/    /'
    fi

    echo ""
}

# 测试开发环境
test_environment "Development"

# 测试生产环境
test_environment "Production"

# 清理临时文件
rm -f /tmp/curio-test-*.log

echo "✅ Configuration test completed!"
echo ""
echo "🚀 To start the application normally:"
echo "   dotnet run --project src/Curio.Api"
echo ""
echo "📝 To create local configuration:"
echo "   ./scripts/config-manager.sh"
