<Project>
  <PropertyGroup>
    <!-- Enable nullable reference types -->
    <Nullable>enable</Nullable>

    <!-- Treat warnings as errors in Release builds -->
    <TreatWarningsAsErrors Condition="'$(Configuration)' == 'Release'">true</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />

    <!-- Enable code analysis -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <AnalysisMode>AllEnabledByDefault</AnalysisMode>

    <!-- Code coverage -->
    <CollectCoverage>true</CollectCoverage>
    <CoverletOutputFormat>opencover</CoverletOutputFormat>
    <CoverletOutput>./coverage/</CoverletOutput>

    <!-- Documentation -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn> <!-- Missing XML comment for publicly visible type or member -->

    <!-- Assembly metadata -->
    <Company>Curio</Company>
    <Product>Curio API</Product>
    <Copyright>Copyright © Curio</Copyright>

    <!-- Source control -->
    <PublishRepositoryUrl>true</PublishRepositoryUrl>
    <EmbedUntrackedSources>true</EmbedUntrackedSources>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>

  <!-- Package references for all projects -->
  <ItemGroup>
    <PackageReference Include="Microsoft.SourceLink.GitHub" Version="8.0.0" PrivateAssets="All"/>
  </ItemGroup>

  <!-- Code analysis rules -->
  <PropertyGroup>
    <!-- Disable specific rules that might be too strict for this project -->
    <NoWarn>$(NoWarn);CA1014</NoWarn> <!-- Mark assemblies with CLSCompliant -->
    <NoWarn>$(NoWarn);CA2007</NoWarn> <!-- Consider calling ConfigureAwait on the awaited task -->
  </PropertyGroup>

  <!-- Test projects configuration -->
  <PropertyGroup Condition="'$(MSBuildProjectName)' == 'Curio.UnitTests' OR '$(MSBuildProjectName)' == 'Curio.IntegrationTests' OR '$(MSBuildProjectName)' == 'Curio.ArchitectureTests'">
    <IsTestProject>true</IsTestProject>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <!-- Disable some rules for test projects -->
    <NoWarn>$(NoWarn);CA1707</NoWarn> <!-- Identifiers should not contain underscores -->
    <NoWarn>$(NoWarn);CA1062</NoWarn> <!-- Validate arguments of public methods -->
    <NoWarn>$(NoWarn);CA2201</NoWarn> <!-- Do not raise reserved exception types -->
  </PropertyGroup>

</Project>
