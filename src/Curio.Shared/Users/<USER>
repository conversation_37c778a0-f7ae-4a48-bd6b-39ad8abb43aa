using Orleans;

namespace Curio.Shared.Users;

// 用户相关的数据传输对象
[GenerateSerializer]
public class UserDto
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string Email { get; set; } = string.Empty;
    [Id(2)] public string Name { get; set; } = string.Empty;
    [Id(3)] public DateTime RegisteredAt { get; set; }
    [Id(4)] public bool IsVerified { get; set; }
}

// 命令对象
[GenerateSerializer]
public class CheckEmailExistsCommand
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
}

[GenerateSerializer]
public class SendVerificationCodeCommand
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string Purpose { get; set; } = "registration"; // "registration" or "login"
    [Id(2)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
}

[GenerateSerializer]
public class RegisterUserCommand
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string Name { get; set; } = string.Empty;
    [Id(2)] public string VerificationCode { get; set; } = string.Empty;
    [Id(3)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
}

[GenerateSerializer]
public class LoginUserCommand
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string VerificationCode { get; set; } = string.Empty;
    [Id(2)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
}

// 查询结果
[GenerateSerializer]
public class EmailExistsResult
{
    [Id(0)] public bool Exists { get; set; }
    [Id(1)] public bool IsVerified { get; set; }
}

[GenerateSerializer]
public class VerificationResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public UserDto? User { get; set; }
}
