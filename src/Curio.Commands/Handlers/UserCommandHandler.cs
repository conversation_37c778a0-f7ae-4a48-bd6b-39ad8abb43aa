using Orleans;
using Curio.Commands.Interfaces;
using Curio.Commands.Converters;
using Curio.Orleans.Interfaces.Users;
using Curio.Shared.Users;
using Microsoft.Extensions.Logging;

namespace Curio.Commands.Handlers;

/// <summary>
/// 用户命令处理器 - 符合CQRS规范的命令端
/// 实现标准化的命令处理接口，包含验证和转换逻辑
/// 分离Web层请求和领域层命令
/// </summary>
public class UserCommandHandler :
    ICommandHandler<RegisterUserCommand, VerificationResult>,
    ICommandHandler<LoginUserCommand, VerificationResult>,
    ICommandHandler<SendVerificationCodeCommand, bool>
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<UserCommandHandler> _logger;

    public UserCommandHandler(IGrainFactory grainFactory, ILogger<UserCommandHandler> logger)
    {
        _grainFactory = grainFactory;
        _logger = logger;
    }

    /// <summary>
    /// 处理用户注册命令
    /// 包含验证码验证、命令转换和领域逻辑调用
    /// </summary>
    public async Task<VerificationResult> HandleAsync(RegisterUserCommand command)
    {
        try
        {
            // 1. 验证验证码
            var isValidCode = await ValidateVerificationCodeAsync(command.Email, command.VerificationCode, "registration");
            if (!isValidCode)
            {
                return new VerificationResult
                {
                    Success = false,
                    Message = "Invalid or expired verification code"
                };
            }

            // 2. 转换为领域命令
            var domainCommand = command.ToDomainCommand("web-api");
            domainCommand.ValidateDomainCommand();

            // 3. 调用领域层
            var userGrain = _grainFactory.GetGrain<IUserGrain>(domainCommand.Email);
            return await userGrain.RegisterUserAsync(domainCommand);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid command for user registration: {Message}", ex.Message);
            return new VerificationResult
            {
                Success = false,
                Message = ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing registration command for {Email}", command.Email);
            return new VerificationResult
            {
                Success = false,
                Message = "Registration failed due to system error"
            };
        }
    }

    /// <summary>
    /// 处理用户登录命令
    /// 包含验证码验证、命令转换和领域逻辑调用
    /// </summary>
    public async Task<VerificationResult> HandleAsync(LoginUserCommand command)
    {
        try
        {
            // 1. 验证验证码
            var isValidCode = await ValidateVerificationCodeAsync(command.Email, command.VerificationCode, "login");
            if (!isValidCode)
            {
                return new VerificationResult
                {
                    Success = false,
                    Message = "Invalid or expired verification code"
                };
            }

            // 2. 转换为领域命令
            var domainCommand = command.ToDomainCommand("web-api");
            domainCommand.ValidateDomainCommand();

            // 3. 调用领域层
            var userGrain = _grainFactory.GetGrain<IUserGrain>(domainCommand.Email);
            return await userGrain.LoginUserAsync(domainCommand);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid command for user login: {Message}", ex.Message);
            return new VerificationResult
            {
                Success = false,
                Message = ex.Message
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing login command for {Email}", command.Email);
            return new VerificationResult
            {
                Success = false,
                Message = "Login failed due to system error"
            };
        }
    }

    /// <summary>
    /// 处理发送验证码命令
    /// </summary>
    public async Task<bool> HandleAsync(SendVerificationCodeCommand command)
    {
        try
        {
            var verificationGrain = _grainFactory.GetGrain<IVerificationGrain>(command.Email);
            return await verificationGrain.SendVerificationCodeAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending verification code to {Email}", command.Email);
            return false;
        }
    }

    /// <summary>
    /// 验证验证码
    /// </summary>
    private async Task<bool> ValidateVerificationCodeAsync(string email, string code, string purpose)
    {
        try
        {
            var verificationGrain = _grainFactory.GetGrain<IVerificationGrain>(email);
            return await verificationGrain.VerifyCodeAsync(code, purpose);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating verification code for {Email}", email);
            return false;
        }
    }

    // 向后兼容方法（逐步移除）
    public Task<VerificationResult> HandleRegisterUserAsync(RegisterUserCommand command) => HandleAsync(command);
    public Task<VerificationResult> HandleLoginUserAsync(LoginUserCommand command) => HandleAsync(command);
    public Task<bool> HandleSendVerificationCodeAsync(SendVerificationCodeCommand command) => HandleAsync(command);
}
