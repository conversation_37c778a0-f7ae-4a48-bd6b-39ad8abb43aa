using Orleans;
using Curio.Orleans.Interfaces.Admins;
using Curio.Shared.Admins;

namespace Curio.Commands.Handlers;

/// <summary>
/// Admin命令处理器 - 简化的CQRS命令端
/// 直接调用Orleans Grains，移除Application层抽象
/// </summary>
public class AdminCommandHandler
{
    private readonly IGrainFactory _grainFactory;

    public AdminCommandHandler(IGrainFactory grainFactory)
    {
        _grainFactory = grainFactory;
    }

    /// <summary>
    /// 管理员登录
    /// </summary>
    public async Task<LoginResult> HandleLoginAsync(AdminLoginCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.Username);
        return await adminGrain.LoginAsync(command);
    }

    /// <summary>
    /// 创建管理员
    /// </summary>
    public async Task<AdminOperationResult> HandleCreateAdminAsync(CreateAdminCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.Username);
        return await adminGrain.CreateAdminAsync(command);
    }

    /// <summary>
    /// 更新管理员信息
    /// </summary>
    public async Task<AdminOperationResult> HandleUpdateAdminAsync(UpdateAdminCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.UpdateAdminAsync(command);
    }

    /// <summary>
    /// 修改管理员密码
    /// </summary>
    public async Task<AdminOperationResult> HandleChangePasswordAsync(ChangeAdminPasswordCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.ChangePasswordAsync(command);
    }

    /// <summary>
    /// 启用两步验证
    /// </summary>
    public async Task<TwoFactorSetupResult> HandleEnableTwoFactorAsync(EnableTwoFactorCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.EnableTwoFactorAsync(command);
    }

    /// <summary>
    /// 禁用两步验证
    /// </summary>
    public async Task<AdminOperationResult> HandleDisableTwoFactorAsync(DisableTwoFactorCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.DisableTwoFactorAsync(command);
    }

    /// <summary>
    /// 验证两步验证码
    /// </summary>
    public async Task<AdminOperationResult> HandleVerifyTwoFactorAsync(VerifyTwoFactorCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.VerifyTwoFactorAsync(command);
    }

    /// <summary>
    /// 重新生成恢复代码
    /// </summary>
    public async Task<RecoveryCodesResult> HandleRegenerateRecoveryCodesAsync(RegenerateRecoveryCodesCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.RegenerateRecoveryCodesAsync(command);
    }

    /// <summary>
    /// 分配角色
    /// </summary>
    public async Task<AdminOperationResult> HandleAssignRoleAsync(AssignRoleCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.AssignRoleAsync(command);
    }

    /// <summary>
    /// 移除角色
    /// </summary>
    public async Task<AdminOperationResult> HandleRemoveRoleAsync(RemoveRoleCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.RemoveRoleAsync(command);
    }

    /// <summary>
    /// 更新管理员状态
    /// </summary>
    public async Task<AdminOperationResult> HandleUpdateStatusAsync(UpdateAdminStatusCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.UpdateStatusAsync(command);
    }

    /// <summary>
    /// 重置管理员密码（管理员操作）
    /// </summary>
    public async Task<AdminOperationResult> HandleResetPasswordAsync(string adminId, string newPassword, string operatorId)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
        return await adminGrain.ResetPasswordAsync(newPassword, operatorId);
    }

    /// <summary>
    /// 解锁管理员账户
    /// </summary>
    public async Task<AdminOperationResult> HandleUnlockAccountAsync(string adminId, string operatorId)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
        return await adminGrain.UnlockAccountAsync(operatorId);
    }

    /// <summary>
    /// 角色相关命令处理
    /// </summary>
    public async Task<RoleOperationResult> HandleCreateRoleAsync(CreateRoleCommand command)
    {
        var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleName);
        return await roleGrain.CreateRoleAsync(command);
    }

    public async Task<RoleOperationResult> HandleUpdateRoleAsync(UpdateRoleCommand command)
    {
        var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleId);
        return await roleGrain.UpdateRoleAsync(command);
    }

    public async Task<RoleOperationResult> HandleDeleteRoleAsync(DeleteRoleCommand command)
    {
        var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleId);
        return await roleGrain.DeleteRoleAsync(command);
    }

    public async Task<RoleOperationResult> HandleAssignPermissionAsync(AssignPermissionCommand command)
    {
        var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleId);
        return await roleGrain.AssignPermissionAsync(command);
    }

    public async Task<RoleOperationResult> HandleRemovePermissionAsync(RemovePermissionCommand command)
    {
        var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleId);
        return await roleGrain.RemovePermissionAsync(command);
    }
}
