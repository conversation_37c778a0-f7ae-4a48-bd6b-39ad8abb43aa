using Orleans;
using Orleans.EventSourcing;
using Microsoft.Extensions.Logging;
using Curio.Orleans.Interfaces.Admins;
using Curio.Shared.Admins;
using Curio.Shared.Users;
using Curio.Orleans.Grains.Base;
using RoleState = Curio.Events.State.RoleState;

namespace Curio.Orleans.Grains.Admins;

public class RoleGrain : ResilientJournaledGrain<RoleState, DomainEvent>, IRoleGrain
{
    private readonly ILogger<RoleGrain> _logger;

    public RoleGrain(ILogger<RoleGrain> logger)
    {
        _logger = logger;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);
        _logger.LogInformation("RoleGrain activated: {RoleId}", this.GetPrimaryKeyString());
    }

    public Task<RoleDto?> GetRoleAsync()
    {
        if (string.IsNullOrEmpty(State.Id))
        {
            return Task.FromResult<RoleDto?>(null);
        }

        return Task.FromResult<RoleDto?>(State.ToDto());
    }

    public async Task<RoleOperationResult> CreateRoleAsync(CreateRoleCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查角色是否已存在
            if (!string.IsNullOrEmpty(State.Id))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role already exists",
                    ErrorCode = "ROLE_EXISTS"
                };
            }

            // 验证输入
            if (string.IsNullOrWhiteSpace(command.RoleName))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role name is required",
                    ErrorCode = "INVALID_INPUT"
                };
            }

            // 创建角色创建事件
            var roleCreatedEvent = new RoleCreatedEvent
            {
                RoleId = this.GetPrimaryKeyString(),
                RoleName = command.RoleName,
                Description = command.Description,
                CreatedAt = DateTime.UtcNow,
                CreatedBy = command.CreatedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(roleCreatedEvent);

            // 如果有初始权限，分配权限
            foreach (var permission in command.Permissions)
            {
                var permissionAssignedEvent = new RolePermissionAssignedEvent
                {
                    RoleId = this.GetPrimaryKeyString(),
                    Resource = permission.Resource,
                    Action = permission.Action,
                    AssignedAt = DateTime.UtcNow,
                    AssignedBy = command.CreatedBy,
                    CommandId = command.CommandId
                };

                RaiseEvent(permissionAssignedEvent);
            }

            await ConfirmEvents();

            return new RoleOperationResult
            {
                Success = true,
                Message = "Role created successfully",
                Role = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role: {RoleName}", command.RoleName);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to create role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> UpdateRoleAsync(UpdateRoleCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查角色是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role not found",
                    ErrorCode = "ROLE_NOT_FOUND"
                };
            }

            // 检查角色是否处于活跃状态
            if (!State.IsActive)
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Cannot update inactive role",
                    ErrorCode = "ROLE_INACTIVE"
                };
            }

            // 验证输入
            if (string.IsNullOrWhiteSpace(command.RoleName))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role name is required",
                    ErrorCode = "INVALID_INPUT"
                };
            }

            // 创建角色更新事件
            var roleUpdatedEvent = new RoleUpdatedEvent
            {
                RoleId = State.Id,
                RoleName = command.RoleName,
                Description = command.Description,
                UpdatedAt = DateTime.UtcNow,
                UpdatedBy = command.UpdatedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(roleUpdatedEvent);
            await ConfirmEvents();

            return new RoleOperationResult
            {
                Success = true,
                Message = "Role updated successfully",
                Role = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role: {RoleId}", command.RoleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to update role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> DeleteRoleAsync(DeleteRoleCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查角色是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role not found",
                    ErrorCode = "ROLE_NOT_FOUND"
                };
            }

            // 检查是否可以删除
            if (!State.CanDelete())
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Cannot delete this role",
                    ErrorCode = "ROLE_CANNOT_DELETE"
                };
            }

            // 检查是否有管理员使用此角色
            var assignedAdminCount = await GetAssignedAdminCountAsync();
            if (assignedAdminCount > 0)
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = $"Cannot delete role: {assignedAdminCount} admins are assigned to this role",
                    ErrorCode = "ROLE_IN_USE"
                };
            }

            // 创建角色删除事件
            var roleDeletedEvent = new RoleDeletedEvent
            {
                RoleId = State.Id,
                RoleName = State.Name,
                DeletedAt = DateTime.UtcNow,
                DeletedBy = command.DeletedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(roleDeletedEvent);
            await ConfirmEvents();

            return new RoleOperationResult
            {
                Success = true,
                Message = "Role deleted successfully"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role: {RoleId}", command.RoleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to delete role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> AssignPermissionAsync(AssignPermissionCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查角色是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role not found",
                    ErrorCode = "ROLE_NOT_FOUND"
                };
            }

            // 检查角色是否可以分配权限
            if (!State.CanAssignPermission(command.Resource, command.Action))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Cannot assign permission to this role",
                    ErrorCode = "ROLE_INACTIVE"
                };
            }

            // 检查权限是否已存在
            if (State.HasPermission(command.Resource, command.Action))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Permission already assigned",
                    ErrorCode = "PERMISSION_EXISTS"
                };
            }

            // 创建权限分配事件
            var permissionAssignedEvent = new RolePermissionAssignedEvent
            {
                RoleId = State.Id,
                Resource = command.Resource,
                Action = command.Action,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = command.AssignedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(permissionAssignedEvent);
            await ConfirmEvents();

            return new RoleOperationResult
            {
                Success = true,
                Message = "Permission assigned successfully",
                Role = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission to role: {RoleId}", command.RoleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to assign permission",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> RemovePermissionAsync(RemovePermissionCommand command)
    {
        try
        {
            // 检查命令是否已处理
            if (State.ProcessedCommands.Contains(command.CommandId))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Command already processed",
                    ErrorCode = "DUPLICATE_COMMAND"
                };
            }

            // 检查角色是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role not found",
                    ErrorCode = "ROLE_NOT_FOUND"
                };
            }

            // 检查权限是否存在
            if (!State.HasPermission(command.Resource, command.Action))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Permission not found",
                    ErrorCode = "PERMISSION_NOT_FOUND"
                };
            }

            // 创建权限移除事件
            var permissionRemovedEvent = new RolePermissionRemovedEvent
            {
                RoleId = State.Id,
                Resource = command.Resource,
                Action = command.Action,
                RemovedAt = DateTime.UtcNow,
                RemovedBy = command.RemovedBy,
                CommandId = command.CommandId
            };

            RaiseEvent(permissionRemovedEvent);
            await ConfirmEvents();

            return new RoleOperationResult
            {
                Success = true,
                Message = "Permission removed successfully",
                Role = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing permission from role: {RoleId}", command.RoleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to remove permission",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public Task<List<PermissionDto>> GetPermissionsAsync()
    {
        var permissions = State.Permissions.Select(p => new PermissionDto
        {
            Resource = p.Resource,
            Action = p.Action,
            ResourceName = GetResourceDisplayName(p.Resource),
            ActionName = GetActionDisplayName(p.Action),
            Description = GetPermissionDescription(p.Resource, p.Action),
            AssignedAt = p.AssignedAt,
            AssignedBy = p.AssignedBy,
            GrantedByRole = State.Name
        }).ToList();

        return Task.FromResult(permissions);
    }

    public Task<bool> HasPermissionAsync(PermissionResource resource, PermissionAction action)
    {
        return Task.FromResult(State.HasPermission(resource, action));
    }

    public async Task<RoleOperationResult> AssignPermissionsAsync(List<PermissionAssignment> permissions, string assignedBy)
    {
        try
        {
            // 检查角色是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role not found",
                    ErrorCode = "ROLE_NOT_FOUND"
                };
            }

            var commandId = Guid.NewGuid().ToString();
            var assignedCount = 0;

            // 批量分配权限
            foreach (var permission in permissions)
            {
                // 检查权限是否已存在
                if (!State.HasPermission(permission.Resource, permission.Action))
                {
                    var permissionAssignedEvent = new RolePermissionAssignedEvent
                    {
                        RoleId = State.Id,
                        Resource = permission.Resource,
                        Action = permission.Action,
                        AssignedAt = DateTime.UtcNow,
                        AssignedBy = assignedBy,
                        CommandId = commandId
                    };

                    RaiseEvent(permissionAssignedEvent);
                    assignedCount++;
                }
            }

            if (assignedCount > 0)
            {
                await ConfirmEvents();
            }

            return new RoleOperationResult
            {
                Success = true,
                Message = $"Successfully assigned {assignedCount} permissions",
                Role = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch assigning permissions to role: {RoleId}", State.Id);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to assign permissions",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> RemovePermissionsAsync(List<PermissionAssignment> permissions, string removedBy)
    {
        try
        {
            // 检查角色是否存在
            if (string.IsNullOrEmpty(State.Id))
            {
                return new RoleOperationResult
                {
                    Success = false,
                    Message = "Role not found",
                    ErrorCode = "ROLE_NOT_FOUND"
                };
            }

            var commandId = Guid.NewGuid().ToString();
            var removedCount = 0;

            // 批量移除权限
            foreach (var permission in permissions)
            {
                // 检查权限是否存在
                if (State.HasPermission(permission.Resource, permission.Action))
                {
                    var permissionRemovedEvent = new RolePermissionRemovedEvent
                    {
                        RoleId = State.Id,
                        Resource = permission.Resource,
                        Action = permission.Action,
                        RemovedAt = DateTime.UtcNow,
                        RemovedBy = removedBy,
                        CommandId = commandId
                    };

                    RaiseEvent(permissionRemovedEvent);
                    removedCount++;
                }
            }

            if (removedCount > 0)
            {
                await ConfirmEvents();
            }

            return new RoleOperationResult
            {
                Success = true,
                Message = $"Successfully removed {removedCount} permissions",
                Role = State.ToDto()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch removing permissions from role: {RoleId}", State.Id);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to remove permissions",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<int> GetAssignedAdminCountAsync()
    {
        // 这里需要查询所有管理员，统计使用此角色的数量
        // 临时实现，返回0
        // 实际实现需要调用查询服务或使用投影
        await Task.CompletedTask;
        return 0;
    }

    // 私有辅助方法
    private static string GetResourceDisplayName(PermissionResource resource)
    {
        return resource switch
        {
            PermissionResource.Users => "用户管理",
            PermissionResource.Admins => "管理员管理",
            PermissionResource.Roles => "角色管理",
            PermissionResource.Permissions => "权限管理",
            PermissionResource.SystemConfig => "系统配置",
            PermissionResource.AuditLogs => "审计日志",
            PermissionResource.EmailTemplates => "邮件模板",
            PermissionResource.Monitoring => "系统监控",
            PermissionResource.ProjectionManagement => "投影管理",
            PermissionResource.DeadLetterQueue => "死信队列管理",
            _ => resource.ToString()
        };
    }

    private static string GetActionDisplayName(PermissionAction action)
    {
        return action switch
        {
            PermissionAction.Read => "查看",
            PermissionAction.Create => "创建",
            PermissionAction.Update => "更新",
            PermissionAction.Delete => "删除",
            PermissionAction.Execute => "执行",
            PermissionAction.Manage => "管理",
            _ => action.ToString()
        };
    }

    private static string GetPermissionDescription(PermissionResource resource, PermissionAction action)
    {
        var resourceName = GetResourceDisplayName(resource);
        var actionName = GetActionDisplayName(action);
        return $"{actionName}{resourceName}";
    }
}
