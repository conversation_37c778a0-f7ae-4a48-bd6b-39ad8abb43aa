using Orleans;
using Orleans.EventSourcing;
using Orleans.Streams;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Curio.Infrastructure.Services;
using Curio.Infrastructure.Configuration;
using Curio.Shared.Users;

namespace Curio.Orleans.Grains.Base;

/// <summary>
/// 提供弹性事件发布能力的基础JournaledGrain
/// 所有业务Grain都应该继承此类而不是直接继承JournaledGrain
/// </summary>
public abstract class ResilientJournaledGrain<TState, TEvent> : JournaledGrain<TState, TEvent>
    where TState : class, new()
    where TEvent : DomainEvent
{
    private IResilientEventPublisher? _eventPublisher;
    protected ILogger Logger { get; private set; } = null!;
    protected IAsyncStream<DomainEvent>? EventStream { get; private set; }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);

        // 获取日志记录器
        Logger = ServiceProvider.GetRequiredService<ILogger<ResilientJournaledGrain<TState, TEvent>>>();

        // 获取StreamProvider（通过Grain的GetStreamProvider方法）
        var streamProvider = this.GetStreamProvider("KafkaStreams");

        // 创建弹性事件发布器
        var options = ServiceProvider.GetRequiredService<IOptions<ResilientPublishOptions>>();
        var publisherLogger = ServiceProvider.GetRequiredService<ILogger<ResilientEventPublisher>>();
        _eventPublisher = new ResilientEventPublisher(streamProvider, publisherLogger, options);

        Logger.LogDebug("ResilientJournaledGrain activated: {GrainType}, {GrainId}",
            GetType().Name, this.GetPrimaryKeyString());
    }

    /// <summary>
    /// JournaledGrain自定义事件处理逻辑
    /// </summary>
    protected override void OnStateChanged()
    {
        base.OnStateChanged();

        // 在状态变更后发布最新的事件
        // 注意：这里需要根据实际的JournaledGrain API调整
    }

    /// <summary>
    /// 发布事件的安全包装方法
    /// </summary>
    protected async Task PublishEventSafelyAsync(TEvent @event)
    {
        if (_eventPublisher == null)
        {
            Logger.LogError("EventPublisher is null, cannot publish event: {EventId}", @event.EventId);
            return;
        }

        try
        {
            await _eventPublisher.PublishAsync(@event, this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to publish event through resilient publisher: {EventId}", @event.EventId);
            // 不重新抛出异常，避免影响业务流程
            // 弹性发布器内部已经处理了重试和失败逻辑
        }
    }

    /// <summary>
    /// 提供批量发布能力
    /// </summary>
    protected async Task PublishEventsAsync(IEnumerable<TEvent> events)
    {
        if (_eventPublisher == null)
        {
            Logger.LogError("EventPublisher is null, cannot publish events");
            return;
        }

        try
        {
            await _eventPublisher.PublishBatchAsync(events, this.GetPrimaryKeyString());
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to publish events batch through resilient publisher");
        }
    }

    /// <summary>
    /// 安全的事件发布方法，包含异常处理
    /// </summary>
    protected async Task<bool> TryPublishEventAsync(TEvent @event)
    {
        try
        {
            await PublishEventSafelyAsync(@event);
            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to publish event: {EventId}", @event.EventId);
            return false;
        }
    }

    /// <summary>
    /// 标准化字符串，替换特殊字符为安全字符
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>标准化后的字符串</returns>
    protected virtual string SanitizeStreamIdentifier(string input)
    {
        return input.Replace("@", "-").Replace(".", "-");
    }

    /// <summary>
    /// 直接向事件流发布事件（不通过弹性发布器）
    /// </summary>
    /// <param name="event">要发布的事件</param>
    protected async Task PublishToStreamAsync(TEvent @event)
    {
        if (EventStream == null)
        {
            Logger.LogWarning("Event stream is not initialized, cannot publish event: {EventId}", @event.EventId);
            return;
        }

        try
        {
            await EventStream.OnNextAsync(@event);
            Logger.LogDebug("Event published to stream: {EventId}", @event.EventId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to publish event to stream: {EventId}", @event.EventId);
            throw;
        }
    }
}
