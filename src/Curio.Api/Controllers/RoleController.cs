using Microsoft.AspNetCore.Mvc;
using Curio.Commands.Handlers;
using Curio.Queries.Handlers;
using Curio.Shared.Admins;
using Curio.Api.Models;
using Curio.Api.Constants;

namespace Curio.Api.Controllers;

[ApiController]
[Route("api/admin/roles")]
internal sealed class RoleController : BaseController
{
    private readonly AdminCommandHandler _commandHandler;
    private readonly AdminQueryHandler _queryHandler;
    private readonly ILogger<RoleController> _logger;

    public RoleController(AdminCommandHandler commandHandler, AdminQueryHandler queryHandler, ILogger<RoleController> logger)
    {
        _commandHandler = commandHandler;
        _queryHandler = queryHandler;
        _logger = logger;
    }

    /// <summary>
    /// 获取角色信息
    /// </summary>
    [HttpGet("{roleId}")]
    public async Task<ActionResult<ApiResponse<RoleDto>>> GetRole(string roleId)
    {
        try
        {
            var role = await _queryHandler.HandleGetRoleAsync(roleId);

            if (role == null)
            {
                return NotFoundError<RoleDto>($"Role with ID '{roleId}' not found");
            }

            return Success(role, "Role retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role: {RoleId}", roleId);
            return InternalError<RoleDto>("Failed to retrieve role");
        }
    }

    /// <summary>
    /// 创建角色
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> CreateRole([FromBody] CreateRoleCommand command)
    {
        try
        {
            command.CreatedBy = "system"; // TODO: 从Token中获取

            var result = await _commandHandler.HandleCreateRoleAsync(command);

            if (result.Success)
            {
                return Created(result, "Role created successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to create role", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role: {RoleName}", command.RoleName);
            return InternalError<RoleOperationResult>("Failed to create role");
        }
    }

    /// <summary>
    /// 更新角色信息
    /// </summary>
    [HttpPut("{roleId}")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> UpdateRole(string roleId, [FromBody] UpdateRoleCommand command)
    {
        try
        {
            command.RoleId = roleId;
            command.UpdatedBy = "system"; // TODO: 从Token中获取

            var result = await _commandHandler.HandleUpdateRoleAsync(command);

            if (result.Success)
            {
                return Success(result, "Role updated successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to update role", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to update role");
        }
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    [HttpDelete("{roleId}")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> DeleteRole(string roleId)
    {
        try
        {
            var command = new DeleteRoleCommand
            {
                RoleId = roleId,
                DeletedBy = "system" // TODO: 从Token中获取
            };

            var result = await _commandHandler.HandleDeleteRoleAsync(command);

            if (result.Success)
            {
                return Success(result, "Role deleted successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to delete role", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to delete role");
        }
    }

    /// <summary>
    /// 获取角色权限
    /// </summary>
    [HttpGet("{roleId}/permissions")]
    public async Task<ActionResult<ApiResponse<List<PermissionDto>>>> GetRolePermissions(string roleId)
    {
        try
        {
            // 临时实现 - 需要在AdminQueryHandler中添加GetRolePermissionsAsync方法
            var role = await _queryHandler.HandleGetRoleAsync(roleId);
            var permissions = role?.Permissions?.ToList() ?? new List<PermissionDto>();
            return Success(permissions, "Role permissions retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role permissions: {RoleId}", roleId);
            return InternalError<List<PermissionDto>>("Failed to retrieve role permissions");
        }
    }

    /// <summary>
    /// 分配权限到角色
    /// </summary>
    [HttpPost("{roleId}/permissions")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> AssignPermission(string roleId, [FromBody] AssignPermissionCommand command)
    {
        try
        {
            command.RoleId = roleId;
            command.AssignedBy = "system"; // TODO: 从Token中获取

            var result = await _commandHandler.HandleAssignPermissionAsync(command);

            if (result.Success)
            {
                return Success(result, "Permission assigned successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to assign permission", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to assign permission");
        }
    }

    /// <summary>
    /// 从角色移除权限
    /// </summary>
    [HttpDelete("{roleId}/permissions")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> RemovePermission(string roleId, [FromBody] RemovePermissionCommand command)
    {
        try
        {
            command.RoleId = roleId;
            command.RemovedBy = "system"; // TODO: 从Token中获取

            var result = await _commandHandler.HandleRemovePermissionAsync(command);

            if (result.Success)
            {
                return Success(result, "Permission removed successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to remove permission", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing permission: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to remove permission");
        }
    }

    /// <summary>
    /// 批量分配权限到角色
    /// </summary>
    [HttpPost("{roleId}/permissions/batch-assign")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> AssignPermissions(string roleId, [FromBody] BatchPermissionRequest request)
    {
        try
        {
            var assignedBy = "system"; // TODO: 从Token中获取
            // 临时实现 - 需要在AdminCommandHandler中添加批量分配方法
            var result = new RoleOperationResult { Success = false, Message = "Batch assign not implemented yet" };

            if (result.Success)
            {
                return Success(result, "Permissions assigned successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to assign permissions", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch assigning permissions: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to assign permissions");
        }
    }

    /// <summary>
    /// 批量移除角色权限
    /// </summary>
    [HttpPost("{roleId}/permissions/batch-remove")]
    public async Task<ActionResult<ApiResponse<RoleOperationResult>>> RemovePermissions(string roleId, [FromBody] BatchPermissionRequest request)
    {
        try
        {
            var removedBy = "system"; // TODO: 从Token中获取
            // 临时实现 - 需要在AdminCommandHandler中添加批量移除方法
            var result = new RoleOperationResult { Success = false, Message = "Batch remove not implemented yet" };

            if (result.Success)
            {
                return Success(result, "Permissions removed successfully");
            }

            return BusinessError<RoleOperationResult>(result.Message ?? "Failed to remove permissions", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch removing permissions: {RoleId}", roleId);
            return InternalError<RoleOperationResult>("Failed to remove permissions");
        }
    }

    /// <summary>
    /// 搜索角色
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResult<RoleSummaryDto>>>> SearchRoles(
        [FromQuery] string? keyword = null,
        [FromQuery] bool? isActive = null,
        [FromQuery] int skip = 0,
        [FromQuery] int take = 20)
    {
        try
        {
            // 临时实现 - 需要在AdminQueryHandler中添加搜索方法
            var result = new PagedResult<RoleSummaryDto> { Items = new List<RoleSummaryDto>(), TotalCount = 0 };
            return Success(result, "Role search completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching roles");
            return InternalError<PagedResult<RoleSummaryDto>>("Failed to search roles");
        }
    }

    /// <summary>
    /// 获取角色统计信息
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<ApiResponse<RoleStatsDto>>> GetRoleStats()
    {
        try
        {
            // 临时实现 - 需要在AdminQueryHandler中添加统计方法
            var stats = new RoleStatsDto { TotalRoles = 0, ActiveRoles = 0 };
            return Success(stats, "Role statistics retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role stats");
            return InternalError<RoleStatsDto>("Failed to retrieve role statistics");
        }
    }

    /// <summary>
    /// 获取所有可用权限
    /// </summary>
    [HttpGet("permissions/available")]
    public async Task<ActionResult<ApiResponse<List<PermissionGroupDto>>>> GetAvailablePermissions()
    {
        try
        {
            var permissions = await _queryHandler.HandleGetPermissionGroupsAsync();
            return Success(permissions, "Available permissions retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available permissions");
            return InternalError<List<PermissionGroupDto>>("Failed to retrieve available permissions");
        }
    }

    /// <summary>
    /// 获取权限分组
    /// </summary>
    [HttpGet("permissions/groups")]
    public async Task<ActionResult<ApiResponse<List<PermissionGroupDto>>>> GetPermissionGroups()
    {
        try
        {
            var groups = await _queryHandler.HandleGetPermissionGroupsAsync();
            return Success(groups, "Permission groups retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting permission groups");
            return InternalError<List<PermissionGroupDto>>("Failed to retrieve permission groups");
        }
    }
}

// 辅助请求模型
internal sealed class BatchPermissionRequest
{
    public List<PermissionAssignment> Permissions { get; set; } = new();
}
