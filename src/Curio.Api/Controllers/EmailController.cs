using Microsoft.AspNetCore.Mvc;
using Curio.Queries.Handlers;
using Curio.Orleans.Interfaces.Email;
using Curio.Api.Models;

namespace Curio.Api.Controllers;

/// <summary>
/// 邮件控制器 - 使用简化的CQRS架构
/// 直接使用查询处理器，移除Application层抽象
/// </summary>
[ApiController]
[Route("api/v2/[controller]")]
internal sealed class EmailV2Controller : BaseController
{
    private readonly EmailQueryHandler _queryHandler;
    private readonly ILogger<EmailV2Controller> _logger;

    public EmailV2Controller(EmailQueryHandler queryHandler, ILogger<EmailV2Controller> logger)
    {
        _queryHandler = queryHandler;
        _logger = logger;
    }

    /// <summary>
    /// Get email sending status for a specific verification event
    /// </summary>
    /// <param name="email">The email address</param>
    /// <param name="eventId">The verification event ID</param>
    /// <returns>Email sending status result</returns>
    [HttpGet("status/{email}/{eventId}")]
    public async Task<ActionResult<ApiResponse<EmailSendingResult>>> GetEmailSendingStatus(string email, string eventId)
    {
        try
        {
            var result = await _queryHandler.HandleGetEmailSendingStatusAsync(email, eventId);

            if (result == null)
            {
                return NotFoundError<EmailSendingResult>($"No email sending record found for event {eventId} and email {email}");
            }

            return Success(result, "Email sending status retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email sending status for {Email}, event {EventId}", email, eventId);
            return InternalError<EmailSendingResult>("Failed to retrieve email sending status");
        }
    }

    /// <summary>
    /// Get email sending statistics for a specific email address
    /// </summary>
    /// <param name="email">The email address</param>
    /// <returns>Email sending statistics</returns>
    [HttpGet("stats/{email}")]
    public async Task<ActionResult<EmailSendingStats>> GetEmailSendingStats(string email)
    {
        try
        {
            var stats = await _queryHandler.HandleGetEmailSendingStatsAsync(email);
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting email sending stats for {Email}", email);
            return StatusCode(500, "Internal server error");
        }
    }
}
