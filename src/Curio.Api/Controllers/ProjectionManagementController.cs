using Microsoft.AspNetCore.Mvc;
using Orleans;
using Curio.Orleans.Interfaces.Projection;
using Curio.Api.Models;

namespace Curio.Api.Controllers;

/// <summary>
/// 投影重建管理API
/// 提供投影重建的管理功能
/// </summary>
[ApiController]
[Route("api/admin/projections")]
internal sealed class ProjectionManagementController : BaseController
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<ProjectionManagementController> _logger;

    public ProjectionManagementController(
        IGrainFactory grainFactory,
        ILogger<ProjectionManagementController> logger)
    {
        _grainFactory = grainFactory;
        _logger = logger;
    }

    /// <summary>
    /// 开始投影重建
    /// </summary>
    /// <param name="projectionName">投影名称</param>
    /// <param name="request">重建请求</param>
    /// <returns>重建任务ID</returns>
    [HttpPost("{projectionName}/rebuild")]
    public async Task<IActionResult> StartRebuild(
        string projectionName,
        [FromBody] StartRebuildRequest request)
    {
        try
        {
            var rebuildId = Guid.NewGuid().ToString();
            var rebuildGrain = _grainFactory.GetGrain<IProjectionRebuildGrain>(rebuildId);

            var rebuildRequest = new RebuildRequest
            {
                ProjectionName = projectionName,
                FromDate = request.FromDate ?? DateTime.UtcNow.AddDays(-7),
                ToDate = request.ToDate,
                ZeroDowntime = request.ZeroDowntime,
                RequestedBy = HttpContext.User?.Identity?.Name ?? "Anonymous",
                Parameters = request.Parameters ?? new Dictionary<string, string>()
            };

            await rebuildGrain.StartRebuildAsync(rebuildRequest);

            _logger.LogInformation("Started projection rebuild: {ProjectionName}, RebuildId: {RebuildId}, RequestedBy: {RequestedBy}",
                projectionName, rebuildId, rebuildRequest.RequestedBy);

            return Accepted(Success(new
            {
                RebuildId = rebuildId,
                ProjectionName = projectionName,
                Status = "Started",
                Message = "Projection rebuild started successfully"
            }));
        }
        catch (InvalidOperationException ex)
        {
            _logger.LogWarning("Invalid rebuild request for projection {ProjectionName}: {Error}", projectionName, ex.Message);
            return BadRequest(ErrorNoData(ex.Message, 400));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start projection rebuild for {ProjectionName}", projectionName);
            return StatusCode(500, ErrorNoData("Failed to start projection rebuild", 500));
        }
    }

    /// <summary>
    /// 获取重建状态
    /// </summary>
    /// <param name="rebuildId">重建任务ID</param>
    /// <returns>重建状态</returns>
    [HttpGet("rebuild/{rebuildId}/status")]
    public async Task<IActionResult> GetRebuildStatus(string rebuildId)
    {
        try
        {
            var rebuildGrain = _grainFactory.GetGrain<IProjectionRebuildGrain>(rebuildId);
            var status = await rebuildGrain.GetRebuildStatusAsync();

            return Ok(Success(status));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get rebuild status for {RebuildId}", rebuildId);
            return StatusCode(500, ErrorNoData("Failed to retrieve rebuild status", 500));
        }
    }

    /// <summary>
    /// 取消投影重建
    /// </summary>
    /// <param name="rebuildId">重建任务ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("rebuild/{rebuildId}/cancel")]
    public async Task<IActionResult> CancelRebuild(string rebuildId)
    {
        try
        {
            var rebuildGrain = _grainFactory.GetGrain<IProjectionRebuildGrain>(rebuildId);
            await rebuildGrain.CancelRebuildAsync();

            _logger.LogInformation("Cancelled projection rebuild: {RebuildId}", rebuildId);

            return Ok(Success(new
            {
                RebuildId = rebuildId,
                Status = "Cancelled",
                Message = "Projection rebuild cancelled successfully"
            }));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ErrorNoData(ex.Message, 400));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel projection rebuild {RebuildId}", rebuildId);
            return StatusCode(500, ErrorNoData("Failed to cancel projection rebuild", 500));
        }
    }

    /// <summary>
    /// 暂停投影重建
    /// </summary>
    /// <param name="rebuildId">重建任务ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("rebuild/{rebuildId}/pause")]
    public async Task<IActionResult> PauseRebuild(string rebuildId)
    {
        try
        {
            var rebuildGrain = _grainFactory.GetGrain<IProjectionRebuildGrain>(rebuildId);
            await rebuildGrain.PauseRebuildAsync();

            _logger.LogInformation("Paused projection rebuild: {RebuildId}", rebuildId);

            return Ok(Success(new
            {
                RebuildId = rebuildId,
                Status = "Paused",
                Message = "Projection rebuild paused successfully"
            }));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ErrorNoData(ex.Message, 400));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause projection rebuild {RebuildId}", rebuildId);
            return StatusCode(500, ErrorNoData("Failed to pause projection rebuild", 500));
        }
    }

    /// <summary>
    /// 恢复投影重建
    /// </summary>
    /// <param name="rebuildId">重建任务ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("rebuild/{rebuildId}/resume")]
    public async Task<IActionResult> ResumeRebuild(string rebuildId)
    {
        try
        {
            var rebuildGrain = _grainFactory.GetGrain<IProjectionRebuildGrain>(rebuildId);
            await rebuildGrain.ResumeRebuildAsync();

            _logger.LogInformation("Resumed projection rebuild: {RebuildId}", rebuildId);

            return Ok(Success(new
            {
                RebuildId = rebuildId,
                Status = "Running",
                Message = "Projection rebuild resumed successfully"
            }));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ErrorNoData(ex.Message, 400));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume projection rebuild {RebuildId}", rebuildId);
            return StatusCode(500, ErrorNoData("Failed to resume projection rebuild", 500));
        }
    }

    /// <summary>
    /// 获取所有支持的投影列表
    /// </summary>
    /// <returns>投影列表</returns>
    [HttpGet("available")]
    public async Task<IActionResult> GetAvailableProjections()
    {
        try
        {
            // 这里可以从配置或者注册中心获取可用的投影列表
            var availableProjections = new[]
            {
                new
                {
                    Name = "UserStatistics",
                    Description = "用户统计投影",
                    SupportedEvents = new[] { "UserRegisteredEvent", "UserLoginAttemptedEvent" }
                },
                new
                {
                    Name = "EmailDeliveryStats",
                    Description = "邮件投递统计投影",
                    SupportedEvents = new[] { "EmailSentSuccessfullyEvent", "EmailSendingFailedEvent" }
                },
                new
                {
                    Name = "UserActivity",
                    Description = "用户活动投影",
                    SupportedEvents = new[] { "UserRegisteredEvent", "UserLoginAttemptedEvent", "VerificationCodeGeneratedEvent" }
                }
            };

            await Task.CompletedTask;
            return Ok(Success(availableProjections));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get available projections");
            return StatusCode(500, ErrorNoData("Failed to retrieve available projections", 500));
        }
    }
}

/// <summary>
/// 开始重建请求模型
/// </summary>
internal sealed class StartRebuildRequest
{
    /// <summary>
    /// 开始时间（如果不指定，默认为7天前）
    /// </summary>
    public DateTime? FromDate { get; set; }

    /// <summary>
    /// 结束时间（如果不指定，处理到当前时间）
    /// </summary>
    public DateTime? ToDate { get; set; }

    /// <summary>
    /// 是否使用零停机时间重建
    /// </summary>
    public bool ZeroDowntime { get; set; } = true;

    /// <summary>
    /// 重建参数
    /// </summary>
    public Dictionary<string, string>? Parameters { get; set; }
}
