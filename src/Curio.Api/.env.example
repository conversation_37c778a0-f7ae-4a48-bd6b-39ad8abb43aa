# Curio API Environment Variables Template
# Copy this file to .env and customize for your local environment
# DO NOT commit .env file to version control

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
ASPNETCORE_ENVIRONMENT=Development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database connection details
DATABASE__PASSWORD=your-database-password

# Alternative: Full connection string (overrides individual settings)
# DATABASE__CONNECTIONSTRING=Host=localhost;Port=5432;Database=curio;Username=curio;Password=your-password

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration
APPLICATION__SECURITY__JWT__SECRETKEY=your-jwt-secret-key-must-be-at-least-32-characters-long-for-security
APPLICATION__SECURITY__ENCRYPTION__KEY=your-encryption-key-32-characters
APPLICATION__SECURITY__ENCRYPTION__SALT=your-salt-16-chars

# =============================================================================
# ORLEANS CONFIGURATION
# =============================================================================
# Orleans connection strings (if empty, will use database connection string)
ORLEANS__CLUSTERING__CONNECTIONSTRING=
ORLEANS__STORAGE__CONNECTIONSTRING=
ORLEANS__STREAMING__CONNECTIONSTRING=
ORLEANS__REMINDERS__CONNECTIONSTRING=

# =============================================================================
# KAFKA CONFIGURATION
# =============================================================================
# Kafka authentication (only needed if using SASL)
KAFKA__SASLUSERNAME=your-kafka-username
KAFKA__SASLPASSWORD=your-kafka-password

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# SMTP Configuration
EMAIL__SMTP__USERNAME=<EMAIL>
EMAIL__SMTP__PASSWORD=your-app-password

# Email sender configuration
EMAIL__DEFAULTSENDER__FROMEMAIL=<EMAIL>
EMAIL__DEFAULTSENDER__REPLYTOEMAIL=<EMAIL>

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# API Base URL (for different environments)
APPLICATION__API__BASEURL=https://localhost:7274

# CORS Origins (comma-separated for multiple origins)
APPLICATION__API__CORS__ALLOWEDORIGINS__0=http://localhost:3000
APPLICATION__API__CORS__ALLOWEDORIGINS__1=http://localhost:5173
APPLICATION__API__CORS__ALLOWEDORIGINS__2=https://localhost:7274

# =============================================================================
# LOCAL ENVIRONMENT VARIANTS
# =============================================================================
# Uncomment and modify for different local setups:

# For Docker Compose setup:
# DATABASE__HOST=localhost
# DATABASE__PORT=5432
# KAFKA__BROKERLIST__0=localhost:9092

# For Cloud/Remote services:
# DATABASE__HOST=your-cloud-db-host
# DATABASE__PORT=5432
# KAFKA__BROKERLIST__0=your-kafka-broker:9092

# For different Orleans cluster:
# ORLEANS__CLUSTERID=curio-cluster-local
# ORLEANS__SERVICEID=curio-service-local
