<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8" />
    <PackageReference Include="Microsoft.Orleans.Client" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Clustering.AdoNet" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Sdk" Version="9.2.1" />
    <PackageReference Include="Scalar.AspNetCore" Version="1.2.44" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Curio.Orleans.Grains\Curio.Orleans.Grains.csproj" />
    <ProjectReference Include="..\Curio.Orleans.Interfaces\Curio.Orleans.Interfaces.csproj" />
    <ProjectReference Include="..\Curio.Shared\Curio.Shared.csproj" />
    <ProjectReference Include="..\Curio.Commands\Curio.Commands.csproj" />
    <ProjectReference Include="..\Curio.Queries\Curio.Queries.csproj" />
    <ProjectReference Include="..\Curio.Projections\Curio.Projections.csproj" />
    <ProjectReference Include="..\Curio.Infrastructure\Curio.Infrastructure.csproj" />
  </ItemGroup>

</Project>
