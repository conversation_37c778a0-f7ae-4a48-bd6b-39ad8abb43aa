namespace Curio.Queries.Interfaces;

/// <summary>
/// 查询处理器基础接口 - CQRS 查询端
/// 明确的读操作边界，专注于数据查询和投影
/// </summary>
/// <typeparam name="TQuery">查询类型</typeparam>
/// <typeparam name="TResult">结果类型</typeparam>
public interface IQueryHandler<in TQuery, TResult>
{
    /// <summary>
    /// 处理查询
    /// </summary>
    /// <param name="query">查询</param>
    /// <returns>查询结果</returns>
    Task<TResult> HandleAsync(TQuery query);
}

/// <summary>
/// 简单查询处理器接口（仅传入参数）
/// </summary>
/// <typeparam name="TResult">结果类型</typeparam>
public interface ISimpleQueryHandler<TResult>
{
    /// <summary>
    /// 处理查询
    /// </summary>
    /// <returns>查询结果</returns>
    Task<TResult> HandleAsync();
}
