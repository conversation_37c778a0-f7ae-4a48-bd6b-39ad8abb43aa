using Orleans;
using Curio.Orleans.Interfaces.Admins;
using Curio.Shared.Admins;

namespace Curio.Queries.Handlers;

/// <summary>
/// Admin查询处理器 - 简化的CQRS查询端
/// 直接查询Orleans Grains，移除Application层抽象
/// </summary>
public class AdminQueryHandler
{
    private readonly IGrainFactory _grainFactory;

    public AdminQueryHandler(IGrainFactory grainFactory)
    {
        _grainFactory = grainFactory;
    }

    /// <summary>
    /// 获取管理员信息
    /// </summary>
    public async Task<AdminDto?> HandleGetAdminAsync(string adminId)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
        return await adminGrain.GetAdminAsync();
    }

    /// <summary>
    /// 检查管理员权限
    /// </summary>
    public async Task<PermissionCheckResult> HandleHasPermissionAsync(CheckPermissionCommand command)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
        return await adminGrain.HasPermissionAsync(command);
    }

    /// <summary>
    /// 获取管理员的所有权限
    /// </summary>
    public async Task<List<PermissionDto>> HandleGetPermissionsAsync(string adminId)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
        return await adminGrain.GetPermissionsAsync();
    }

    /// <summary>
    /// 获取管理员的所有角色
    /// </summary>
    public async Task<List<RoleDto>> HandleGetRolesAsync(string adminId)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
        return await adminGrain.GetRolesAsync();
    }

    /// <summary>
    /// 获取剩余恢复代码数量
    /// </summary>
    public async Task<int> HandleGetRemainingRecoveryCodesCountAsync(string adminId)
    {
        var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
        return await adminGrain.GetRemainingRecoveryCodesCountAsync();
    }

    /// <summary>
    /// 角色相关查询
    /// </summary>
    public async Task<RoleDto?> HandleGetRoleAsync(string roleId)
    {
        var roleGrain = _grainFactory.GetGrain<IRoleGrain>(roleId);
        return await roleGrain.GetRoleAsync();
    }

    /// <summary>
    /// 获取管理员统计信息 - 临时实现
    /// </summary>
    public async Task<AdminStatsDto> HandleGetAdminStatsAsync()
    {
        // 这里需要实现统计逻辑，可能需要专门的统计Grain
        // 临时返回默认值
        await Task.CompletedTask;
        return new AdminStatsDto
        {
            TotalAdmins = 0,
            ActiveAdmins = 0
        };
    }

    /// <summary>
    /// 搜索管理员 - 临时实现
    /// </summary>
    public async Task<PagedResult<AdminDto>> HandleSearchAdminsAsync(AdminSearchRequest request)
    {
        // 这里需要实现搜索逻辑，可能需要专门的搜索Grain或投影
        // 临时返回空结果
        await Task.CompletedTask;
        return new PagedResult<AdminDto>
        {
            Items = [],
            TotalCount = 0
        };
    }

    /// <summary>
    /// 获取所有角色 - 临时实现
    /// </summary>
    public async Task<List<RoleDto>> HandleGetAllRolesAsync()
    {
        // 这里需要实现获取所有角色的逻辑，可能需要专门的角色索引Grain
        // 临时返回空列表
        await Task.CompletedTask;
        return new List<RoleDto>();
    }

    /// <summary>
    /// 获取权限组 - 临时实现
    /// </summary>
    public async Task<List<PermissionGroupDto>> HandleGetPermissionGroupsAsync()
    {
        // 这里需要实现获取权限组的逻辑
        // 临时返回空列表
        await Task.CompletedTask;
        return new List<PermissionGroupDto>();
    }
}
