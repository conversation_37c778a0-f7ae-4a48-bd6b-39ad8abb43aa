using Curio.Shared.Users;

namespace Curio.Infrastructure.Services;

/// <summary>
/// 弹性事件发布器接口
/// 提供带重试机制的事件发布能力
/// </summary>
public interface IResilientEventPublisher
{
    /// <summary>
    /// 发布单个事件
    /// </summary>
    Task PublishAsync<T>(T @event, string streamId) where T : DomainEvent;

    /// <summary>
    /// 批量发布事件
    /// </summary>
    Task PublishBatchAsync<T>(IEnumerable<T> events, string streamId) where T : DomainEvent;
}
