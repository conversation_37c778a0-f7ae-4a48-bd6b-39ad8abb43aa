using System.Text;
using HandlebarsDotNet;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Curio.Infrastructure.Configuration;

namespace Curio.Infrastructure.Services;

public class HandlebarsEmailTemplateService : IEmailTemplateService
{
    private readonly EmailSettings _settings;
    private readonly IMemoryCache _cache;
    private readonly ILogger<HandlebarsEmailTemplateService> _logger;
    private readonly IHandlebars _handlebars;
    private readonly string _templatesBasePath;

    public HandlebarsEmailTemplateService(
        IOptions<EmailSettings> settings,
        IMemoryCache cache,
        ILogger<HandlebarsEmailTemplateService> logger)
    {
        _settings = settings.Value;
        _cache = cache;
        _logger = logger;

        _handlebars = Handlebars.Create();
        RegisterHelpers();

        _templatesBasePath = Path.IsPathRooted(_settings.Templates.TemplatesDirectory)
            ? _settings.Templates.TemplatesDirectory
            : Path.Combine(Directory.GetCurrentDirectory(), _settings.Templates.TemplatesDirectory);

        EnsureTemplatesDirectoryExists();
    }

    public async Task<string> RenderTemplateAsync(string templateName, object model)
    {
        try
        {
            var template = await GetCompiledTemplateAsync(templateName);
            var result = template(model);

            _logger.LogDebug("Template {TemplateName} rendered successfully", templateName);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to render template {TemplateName}", templateName);
            throw;
        }
    }

    public async Task<bool> TemplateExistsAsync(string templateName)
    {
        var templatePath = GetTemplatePath(templateName);
        var exists = File.Exists(templatePath);

        await Task.CompletedTask;
        return exists;
    }

    private async Task<HandlebarsTemplate<object, object>> GetCompiledTemplateAsync(string templateName)
    {
        var cacheKey = $"EmailTemplate_{templateName}";

        if (_settings.Templates.EnableCaching && _cache.TryGetValue(cacheKey, out HandlebarsTemplate<object, object>? cachedTemplate) && cachedTemplate != null)
        {
            return cachedTemplate;
        }

        var templatePath = GetTemplatePath(templateName);

        if (!File.Exists(templatePath))
        {
            throw new FileNotFoundException($"Email template not found: {templatePath}");
        }

        var templateContent = await File.ReadAllTextAsync(templatePath, Encoding.UTF8);
        var compiledTemplate = _handlebars.Compile(templateContent);

        if (_settings.Templates.EnableCaching)
        {
            var cacheOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_settings.Templates.CacheExpirationMinutes),
                Priority = CacheItemPriority.Normal
            };

            _cache.Set(cacheKey, compiledTemplate, cacheOptions);
            _logger.LogDebug("Template {TemplateName} cached successfully", templateName);
        }

        return compiledTemplate;
    }

    private string GetTemplatePath(string templateName)
    {
        // Ensure the template name is safe (no directory traversal)
        var safeTemplateName = Path.GetFileName(templateName);
        return Path.Combine(_templatesBasePath, safeTemplateName);
    }

    private void EnsureTemplatesDirectoryExists()
    {
        if (!Directory.Exists(_templatesBasePath))
        {
            Directory.CreateDirectory(_templatesBasePath);
            _logger.LogInformation("Created email templates directory: {Path}", _templatesBasePath);
        }
    }

    private void RegisterHelpers()
    {
        // Date formatting helper
        _handlebars.RegisterHelper("formatDate", (writer, context, parameters) =>
        {
            if (parameters.Length > 0 && parameters[0] is DateTime date)
            {
                var format = parameters.Length > 1 ? parameters[1].ToString() : "yyyy-MM-dd";
                writer.WriteSafeString(date.ToString(format));
            }
        });

        // Currency formatting helper
        _handlebars.RegisterHelper("formatCurrency", (writer, context, parameters) =>
        {
            if (parameters.Length > 0 && decimal.TryParse(parameters[0].ToString(), out var amount))
            {
                var currency = parameters.Length > 1 ? parameters[1].ToString() : "USD";
                writer.WriteSafeString($"{amount:C} {currency}");
            }
        });

        // URL encoding helper
        _handlebars.RegisterHelper("urlEncode", (writer, context, parameters) =>
        {
            if (parameters.Length > 0 && parameters[0] != null)
            {
                var encoded = Uri.EscapeDataString(parameters[0].ToString() ?? "");
                writer.WriteSafeString(encoded);
            }
        });

        // Conditional helper
        _handlebars.RegisterHelper("ifEquals", (writer, options, context, parameters) =>
        {
            if (parameters.Length >= 2 &&
                string.Equals(parameters[0]?.ToString(), parameters[1]?.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                options.Template(writer, context);
            }
            else
            {
                options.Inverse(writer, context);
            }
        });

        // Loop with index helper
        _handlebars.RegisterHelper("eachWithIndex", (writer, options, context, parameters) =>
        {
            if (parameters.Length > 0 && parameters[0] is IEnumerable<object> items)
            {
                var index = 0;
                foreach (var item in items)
                {
                    var blockParams = new Dictionary<string, object>
                    {
                        ["index"] = index,
                        ["isFirst"] = index == 0,
                        ["isLast"] = index == items.Count() - 1
                    };

                    options.Template(writer, item);
                    index++;
                }
            }
        });

        _logger.LogDebug("Handlebars helpers registered successfully");
    }
}
