namespace Curio.Infrastructure.Services;

public class EmailServiceAdapter : IEmailService
{
    private readonly ISmtpEmailService _smtpEmailService;

    public EmailServiceAdapter(ISmtpEmailService smtpEmailService)
    {
        _smtpEmailService = smtpEmailService;
    }

    public async Task<bool> SendVerificationCodeAsync(string email, string code, string purpose)
    {
        var templateModel = new
        {
            code = code,
            purpose = purpose,
            expiresAt = DateTime.UtcNow.AddMinutes(10),
            email = email
        };

        var subject = purpose switch
        {
            "registration" => "欢迎注册 Curio - 验证码",
            "login" => "Curio 登录验证码",
            _ => "Curio 验证码"
        };

        return await _smtpEmailService.SendTemplatedEmailAsync("verification-code", email, subject, templateModel);
    }
}
