using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Curio.Infrastructure.Services;
using Curio.Infrastructure.Configuration;
using Curio.Infrastructure.HealthChecks;
using Npgsql;

namespace Curio.Infrastructure;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Configure all settings
        services.Configure<DatabaseSettings>(configuration.GetSection(DatabaseSettings.SectionName));
        services.Configure<KafkaSettings>(configuration.GetSection(KafkaSettings.SectionName));
        services.Configure<OrleansSettings>(configuration.GetSection(OrleansSettings.SectionName));
        services.Configure<ApplicationSettings>(configuration.GetSection(ApplicationSettings.SectionName));
        services.Configure<EmailSettings>(configuration.GetSection(EmailSettings.SectionName));
        services.Configure<ResilientPublishOptions>(configuration.GetSection(ResilientPublishOptions.SectionName));

        // Register email services
        services.AddSingleton<IEmailTemplateService, HandlebarsEmailTemplateService>();
        services.AddScoped<ISmtpEmailService, SmtpEmailService>();

        // For backward compatibility, register the old IEmailService interface
        services.AddScoped<IEmailService>(provider =>
        {
            var smtpService = provider.GetRequiredService<ISmtpEmailService>();
            return new EmailServiceAdapter(smtpService);
        });

        // Register security services
        services.AddScoped<ITotpService, TotpService>();

        // Note: IResilientEventPublisher is not registered here because it requires IStreamProvider
        // which is only available in Orleans grain context. It's created within grains as needed.

        // Add memory cache for template caching
        services.AddMemoryCache();

        return services;
    }

    /// <summary>
    /// Get database connection string from configuration
    /// </summary>
    public static string GetDatabaseConnectionString(this IConfiguration configuration)
    {
        var databaseSettings = configuration.GetSection(DatabaseSettings.SectionName).Get<DatabaseSettings>() ?? new DatabaseSettings();
        return databaseSettings.GetConnectionString();
    }

    /// <summary>
    /// Get Orleans connection string from configuration
    /// </summary>
    public static string GetOrleansConnectionString(this IConfiguration configuration, string connectionType = "clustering")
    {
        var orleansSettings = configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();
        var databaseConnectionString = configuration.GetDatabaseConnectionString();

        return connectionType.ToLower() switch
        {
            "clustering" => !string.IsNullOrEmpty(orleansSettings.Clustering.ConnectionString)
                ? orleansSettings.Clustering.ConnectionString
                : databaseConnectionString,
            "storage" => !string.IsNullOrEmpty(orleansSettings.Storage.ConnectionString)
                ? orleansSettings.Storage.ConnectionString
                : databaseConnectionString,
            "reminders" => !string.IsNullOrEmpty(orleansSettings.Reminders.ConnectionString)
                ? orleansSettings.Reminders.ConnectionString
                : databaseConnectionString,
            _ => databaseConnectionString
        };
    }

    /// <summary>
    /// Get Kafka brokers from configuration
    /// </summary>
    public static string[] GetKafkaBrokers(this IConfiguration configuration)
    {
        var kafkaSettings = configuration.GetSection(KafkaSettings.SectionName).Get<KafkaSettings>() ?? new KafkaSettings();
        return kafkaSettings.BrokerList;
    }

    /// <summary>
    /// Get Kafka broker list as string
    /// </summary>
    public static string GetKafkaBrokersString(this IConfiguration configuration)
    {
        return string.Join(",", configuration.GetKafkaBrokers());
    }

    /// <summary>
    /// Get Kafka connection string from Orleans streaming configuration
    /// </summary>
    public static string GetKafkaConnectionString(this IConfiguration configuration)
    {
        var orleansSettings = configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();

        if (!string.IsNullOrEmpty(orleansSettings.Streaming.ConnectionString))
        {
            return orleansSettings.Streaming.ConnectionString;
        }

        return configuration.GetKafkaBrokersString();
    }

    /// <summary>
    /// Add health checks for infrastructure components
    /// </summary>
    public static IServiceCollection AddInfrastructureHealthChecks(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHealthChecks()
            // PostgreSQL health check
            .AddCheck<PostgreSqlHealthCheck>("postgresql", HealthStatus.Unhealthy, new[] { "database", "postgresql" })
            // Kafka health check
            .AddCheck<KafkaHealthCheck>("kafka", HealthStatus.Unhealthy, new[] { "messaging", "kafka" })
            // Orleans cluster health check (需要在API项目中添加，因为需要IGrainFactory)
            .AddCheck<OrleansClusterHealthCheck>("orleans", HealthStatus.Unhealthy, new[] { "orleans", "cluster" });

        return services;
    }

    /// <summary>
    /// Add background services for infrastructure
    /// </summary>
    public static IServiceCollection AddInfrastructureBackgroundServices(this IServiceCollection services)
    {
        // Add dead letter queue monitor
        services.AddHostedService<DeadLetterQueueMonitor>();

        return services;
    }
}
