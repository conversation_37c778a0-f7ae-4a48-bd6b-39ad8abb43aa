using Orleans;
using Orleans.Streams;
using Microsoft.Extensions.Logging;
using Curio.Shared.Users;

namespace Curio.Events.Handlers;

/// <summary>
/// 事件分发器 - 优化Orleans事件流处理
/// 集中管理事件到流的分发，避免循环依赖
/// </summary>
public class EventDispatcher : Grain, IEventDispatcher
{
    private readonly ILogger<EventDispatcher> _logger;
    private readonly Dictionary<string, IAsyncStream<DomainEvent>> _eventStreams = new();

    public EventDispatcher(ILogger<EventDispatcher> logger)
    {
        _logger = logger;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);

        // 初始化事件流
        var streamProvider = this.GetStreamProvider("KafkaStreams");

        // 用户事件流
        _eventStreams["user-events"] = streamProvider.GetStream<DomainEvent>("user-events", "dispatcher");

        // 邮件事件流
        _eventStreams["email-events"] = streamProvider.GetStream<DomainEvent>("email-events", "dispatcher");

        _logger.LogInformation("EventDispatcher activated: {GrainId}", this.GetPrimaryKeyString());
    }

    /// <summary>
    /// 分发用户事件到相关流
    /// </summary>
    public async Task DispatchUserEventAsync(DomainEvent userEvent)
    {
        try
        {
            // 发布到用户事件流，由投影系统订阅处理
            if (_eventStreams.TryGetValue("user-events", out var stream))
            {
                await stream.OnNextAsync(userEvent);
            }

            _logger.LogDebug("User event dispatched: {EventType} - {EventId}",
                userEvent.GetType().Name, userEvent.EventId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to dispatch user event: {EventId}", userEvent.EventId);
        }
    }

    /// <summary>
    /// 分发邮件事件到相关流
    /// </summary>
    public async Task DispatchEmailEventAsync(DomainEvent emailEvent)
    {
        try
        {
            // 发布到邮件事件流
            if (_eventStreams.TryGetValue("email-events", out var stream))
            {
                await stream.OnNextAsync(emailEvent);
            }

            _logger.LogDebug("Email event dispatched: {EventType} - {EventId}",
                emailEvent.GetType().Name, emailEvent.EventId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to dispatch email event: {EventId}", emailEvent.EventId);
        }
    }
}

/// <summary>
/// 事件分发器接口
/// </summary>
public interface IEventDispatcher : IGrainWithStringKey
{
    /// <summary>
    /// 分发用户事件
    /// </summary>
    Task DispatchUserEventAsync(DomainEvent userEvent);

    /// <summary>
    /// 分发邮件事件
    /// </summary>
    Task DispatchEmailEventAsync(DomainEvent emailEvent);
}
