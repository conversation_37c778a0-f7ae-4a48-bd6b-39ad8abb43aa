<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Orleans.Core.Abstractions" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Streaming" Version="9.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Curio.Shared\Curio.Shared.csproj" />
  </ItemGroup>

  <!-- 注意：这里不添加Projections项目引用，避免循环依赖 -->
  <!-- Events项目应该是最底层的，不依赖具体的投影实现 -->

</Project>
