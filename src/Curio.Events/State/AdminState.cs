using Orleans;
using Curio.Shared.Users;
using Curio.Shared.Admins;

namespace Curio.Events.State;

[GenerateSerializer]
public class AdminState
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public string Email { get; set; } = string.Empty;
    [Id(3)] public string Name { get; set; } = string.Empty;
    [Id(4)] public string PasswordHash { get; set; } = string.Empty;
    [Id(5)] public AdminStatus Status { get; set; } = AdminStatus.PendingSetup;
    [Id(6)] public DateTime CreatedAt { get; set; }
    [Id(7)] public DateTime? LastLoginAt { get; set; }
    [Id(8)] public DateTime? UpdatedAt { get; set; }
    [Id(9)] public string CreatedBy { get; set; } = string.Empty;
    [Id(10)] public List<string> RoleIds { get; set; } = new();
    [Id(11)] public HashSet<string> ProcessedCommands { get; set; } = new();

    // 2FA相关属性
    [Id(12)] public bool TwoFactorEnabled { get; set; } = false;
    [Id(13)] public string? TwoFactorSecret { get; set; }
    [Id(14)] public List<string> RecoveryCodes { get; set; } = new();
    [Id(15)] public DateTime? TwoFactorEnabledAt { get; set; }

    // 安全相关属性
    [Id(16)] public int FailedLoginAttempts { get; set; } = 0;
    [Id(17)] public DateTime? LockedUntil { get; set; }
    [Id(18)] public DateTime? PasswordChangedAt { get; set; }
    [Id(19)] public bool MustChangePassword { get; set; } = true;
    [Id(20)] public string? LastLoginIp { get; set; }

    // 业务规则验证
    public bool CanLogin(string username, string password)
    {
        if (Status != AdminStatus.Active)
            return false;

        if (IsLocked())
            return false;

        if (FailedLoginAttempts >= 5) // 最多5次失败尝试
            return false;

        return !string.IsNullOrEmpty(Username) &&
               !string.IsNullOrEmpty(PasswordHash);
    }

    public bool CanChangePassword()
    {
        return Status == AdminStatus.Active || Status == AdminStatus.PendingSetup;
    }

    public bool CanAssignRole()
    {
        return Status == AdminStatus.Active;
    }

    public bool IsLocked()
    {
        return Status == AdminStatus.Locked ||
               (LockedUntil.HasValue && LockedUntil.Value > DateTime.UtcNow);
    }

    public bool RequiresTwoFactor()
    {
        return TwoFactorEnabled;
    }

    // 状态转换方法
    public void ApplyEvent(AdminCreatedEvent evt)
    {
        Id = evt.AdminId;
        Username = evt.Username;
        Email = evt.Email;
        Name = evt.Name;
        Status = AdminStatus.PendingSetup;
        CreatedAt = evt.CreatedAt;
        CreatedBy = evt.CreatedBy;
        MustChangePassword = true;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(AdminPasswordChangedEvent evt)
    {
        PasswordHash = evt.PasswordHash;
        PasswordChangedAt = evt.ChangedAt;
        MustChangePassword = false;
        UpdatedAt = evt.ChangedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(AdminLoginAttemptedEvent evt)
    {
        if (evt.Success)
        {
            LastLoginAt = evt.AttemptedAt;
            LastLoginIp = evt.IpAddress;
            FailedLoginAttempts = 0;
            if (Status == AdminStatus.PendingSetup && !MustChangePassword)
            {
                Status = AdminStatus.Active;
            }
        }
        else
        {
            FailedLoginAttempts++;
            if (FailedLoginAttempts >= 5)
            {
                Status = AdminStatus.Locked;
                LockedUntil = DateTime.UtcNow.AddMinutes(30); // 锁定30分钟
            }
        }

        UpdatedAt = evt.AttemptedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(TwoFactorEnabledEvent evt)
    {
        TwoFactorEnabled = true;
        TwoFactorSecret = evt.SecretKey;
        TwoFactorEnabledAt = evt.EnabledAt;
        RecoveryCodes.Clear();
        foreach (var code in evt.RecoveryCodes)
        {
            RecoveryCodes.Add(code);
        }
        UpdatedAt = evt.EnabledAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(TwoFactorDisabledEvent evt)
    {
        TwoFactorEnabled = false;
        TwoFactorSecret = null;
        TwoFactorEnabledAt = null;
        RecoveryCodes.Clear();
        UpdatedAt = evt.DisabledAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(AdminRoleAssignedEvent evt)
    {
        if (!RoleIds.Contains(evt.RoleId))
        {
            RoleIds.Add(evt.RoleId);
        }
        UpdatedAt = evt.AssignedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(AdminRoleRemovedEvent evt)
    {
        RoleIds.Remove(evt.RoleId);
        UpdatedAt = evt.RemovedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(AdminStatusChangedEvent evt)
    {
        Status = evt.NewStatus;
        UpdatedAt = evt.ChangedAt;

        // 如果状态改为解锁，重置失败尝试次数
        if (evt.NewStatus == AdminStatus.Active)
        {
            FailedLoginAttempts = 0;
            LockedUntil = null;
        }

        ProcessedCommands.Add(evt.CommandId);
    }

    public void ApplyEvent(RecoveryCodeUsedEvent evt)
    {
        // 移除已使用的恢复代码
        var normalizedUsedCode = evt.RecoveryCode.Replace("-", "").Replace(" ", "").ToLower();

        RecoveryCodes.RemoveAll(code =>
        {
            var normalizedCode = code.Replace("-", "").Replace(" ", "").ToLower();
            return normalizedCode == normalizedUsedCode;
        });

        UpdatedAt = evt.UsedAt;
        ProcessedCommands.Add(evt.CommandId);
    }

    // 转换为DTO
    public AdminDto ToDto()
    {
        var dto = new AdminDto
        {
            Id = Id,
            Username = Username,
            Email = Email,
            Name = Name,
            Status = Status,
            CreatedAt = CreatedAt,
            LastLoginAt = LastLoginAt,
            UpdatedAt = UpdatedAt,
            CreatedBy = CreatedBy,
            TwoFactorEnabled = TwoFactorEnabled,
            TwoFactorEnabledAt = TwoFactorEnabledAt,
            FailedLoginAttempts = FailedLoginAttempts,
            IsLocked = IsLocked(),
            MustChangePassword = MustChangePassword,
            LastLoginIp = LastLoginIp
        };

        // 填充只读的RoleIds集合
        foreach (var roleId in RoleIds)
        {
            dto.RoleIds.Add(roleId);
        }

        return dto;
    }
}
