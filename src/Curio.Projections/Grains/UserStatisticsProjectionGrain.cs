using Orleans;
using Orleans.Streams;
using Microsoft.Extensions.Logging;
using Curio.Projections.Interfaces;
using Curio.Shared.Users;

namespace Curio.Projections.Grains;

/// <summary>
/// 用户统计投影 Grain - 利用Orleans的强大能力实现实时投影
/// 每个统计维度一个Grain，自动分布式处理
/// </summary>
public class UserStatisticsProjectionGrain : Grain<UserStatistics>, IUserStatisticsProjectionGrain
{
    private readonly ILogger<UserStatisticsProjectionGrain> _logger;

    public UserStatisticsProjectionGrain(ILogger<UserStatisticsProjectionGrain> logger)
    {
        _logger = logger;
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);

        // 初始化状态
        if (State == null)
        {
            State = new UserStatistics
            {
                LastUpdated = DateTime.UtcNow
            };
        }

        // 订阅用户事件流
        var streamProvider = this.GetStreamProvider("KafkaStreams");
        var userEventStream = streamProvider.GetStream<DomainEvent>("user-events", "all");

        await userEventStream.SubscribeAsync(async (evt, token) =>
        {
            await ProcessUserEventAsync(evt);
        });

        _logger.LogInformation("UserStatisticsProjectionGrain activated: {GrainId}", this.GetPrimaryKeyString());
    }

    public async Task ProcessUserEventAsync(DomainEvent userEvent)
    {
        switch (userEvent)
        {
            case UserRegisteredEvent registered:
                await ProcessUserRegisteredAsync(registered);
                break;
            case UserLoginAttemptedEvent loginAttempt:
                await ProcessLoginAttemptAsync(loginAttempt);
                break;
        }
    }

    private async Task ProcessUserRegisteredAsync(UserRegisteredEvent evt)
    {
        State.TotalUsers++;
        State.VerifiedUsers++; // 注册成功即为已验证

        // 统计域名分布
        var domain = evt.Email.Split('@')[1];
        State.UsersByDomain[domain] = State.UsersByDomain.GetValueOrDefault(domain, 0) + 1;

        State.LastUpdated = DateTime.UtcNow;
        await WriteStateAsync();

        _logger.LogDebug("Processed UserRegisteredEvent: {Email}, Total users: {Total}",
            evt.Email, State.TotalUsers);
    }

    private async Task ProcessLoginAttemptAsync(UserLoginAttemptedEvent evt)
    {
        // 可以添加登录统计逻辑
        State.LastUpdated = DateTime.UtcNow;
        await WriteStateAsync();

        _logger.LogDebug("Processed UserLoginAttemptedEvent: {Email}, Success: {Success}",
            evt.Email, evt.Success);
    }

    public Task<UserStatistics> GetStatisticsAsync()
    {
        return Task.FromResult(State);
    }

    public Task<DomainStatistics> GetDomainStatisticsAsync(string domain)
    {
        var domainStats = new DomainStatistics
        {
            Domain = domain,
            TotalUsers = State.UsersByDomain.GetValueOrDefault(domain, 0),
            VerifiedUsers = State.UsersByDomain.GetValueOrDefault(domain, 0), // 简化处理
            LastUpdated = State.LastUpdated
        };

        return Task.FromResult(domainStats);
    }
}
