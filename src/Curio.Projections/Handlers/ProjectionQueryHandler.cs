using Orleans;
using Curio.Projections.Interfaces;

namespace Curio.Projections.Handlers;

/// <summary>
/// 投影查询处理器 - 利用Orleans的分布式投影能力
/// 每个投影都是独立的Orleans Grain，天然分布式和可扩展
/// </summary>
public class ProjectionQueryHandler
{
    private readonly IGrainFactory _grainFactory;

    public ProjectionQueryHandler(IGrainFactory grainFactory)
    {
        _grainFactory = grainFactory;
    }

    /// <summary>
    /// 获取全局用户统计信息
    /// </summary>
    public async Task<UserStatistics> GetGlobalUserStatisticsAsync()
    {
        var statsGrain = _grainFactory.GetGrain<IUserStatisticsProjectionGrain>("global");
        return await statsGrain.GetStatisticsAsync();
    }

    /// <summary>
    /// 获取特定域名的用户统计
    /// </summary>
    public async Task<DomainStatistics> GetDomainStatisticsAsync(string domain)
    {
        var statsGrain = _grainFactory.GetGrain<IUserStatisticsProjectionGrain>("global");
        return await statsGrain.GetDomainStatisticsAsync(domain);
    }

    /// <summary>
    /// 获取多个域名的统计信息（并行查询）
    /// 充分利用Orleans的并发能力
    /// </summary>
    public async Task<Dictionary<string, DomainStatistics>> GetMultipleDomainStatisticsAsync(IEnumerable<string> domains)
    {
        var tasks = domains.Select(async domain =>
        {
            var stats = await GetDomainStatisticsAsync(domain);
            return new KeyValuePair<string, DomainStatistics>(domain, stats);
        });

        var results = await Task.WhenAll(tasks);
        return results.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// 获取投影系统健康状态
    /// </summary>
    public async Task<ProjectionHealthStatus> GetProjectionHealthAsync()
    {
        var statsGrain = _grainFactory.GetGrain<IUserStatisticsProjectionGrain>("global");
        var stats = await statsGrain.GetStatisticsAsync();

        return new ProjectionHealthStatus
        {
            IsHealthy = true,
            LastUpdated = stats.LastUpdated,
            TotalProjections = 1, // 当前只有一个投影
            Message = "All projections are running normally"
        };
    }
}

/// <summary>
/// 投影系统健康状态
/// </summary>
[GenerateSerializer]
public class ProjectionHealthStatus
{
    [Id(0)] public bool IsHealthy { get; set; }
    [Id(1)] public DateTime LastUpdated { get; set; }
    [Id(2)] public int TotalProjections { get; set; }
    [Id(3)] public string Message { get; set; } = string.Empty;
}
