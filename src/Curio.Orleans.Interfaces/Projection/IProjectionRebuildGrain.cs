using Orleans;

namespace Curio.Orleans.Interfaces.Projection;

/// <summary>
/// 投影重建请求
/// </summary>
[GenerateSerializer]
public class RebuildRequest
{
    [Id(0)] public string ProjectionName { get; set; } = string.Empty;
    [Id(1)] public DateTime FromDate { get; set; } = DateTime.UtcNow.AddDays(-7);
    [Id(2)] public DateTime? ToDate { get; set; }
    [Id(3)] public bool ZeroDowntime { get; set; } = true;
    [Id(4)] public string RequestedBy { get; set; } = string.Empty;
    [Id(5)] public Dictionary<string, string> Parameters { get; set; } = new();
}

/// <summary>
/// 投影重建状态
/// </summary>
[GenerateSerializer]
public class RebuildStatus
{
    [Id(0)] public string RebuildId { get; set; } = string.Empty;
    [Id(1)] public string ProjectionName { get; set; } = string.Empty;
    [Id(2)] public string Status { get; set; } = string.Empty; // Pending, Running, Completed, Failed
    [Id(3)] public DateTime StartedAt { get; set; }
    [Id(4)] public DateTime? CompletedAt { get; set; }
    [Id(5)] public long ProcessedEvents { get; set; }
    [Id(6)] public long TotalEstimatedEvents { get; set; }
    [Id(7)] public string? ErrorMessage { get; set; }
    [Id(8)] public double ProgressPercentage { get; set; }
    [Id(9)] public TimeSpan? EstimatedTimeRemaining { get; set; }
}

/// <summary>
/// 投影重建Grain接口
/// </summary>
public interface IProjectionRebuildGrain : IGrainWithStringKey
{
    /// <summary>
    /// 开始投影重建
    /// </summary>
    Task StartRebuildAsync(RebuildRequest request);

    /// <summary>
    /// 获取重建状态
    /// </summary>
    Task<RebuildStatus> GetRebuildStatusAsync();

    /// <summary>
    /// 取消重建
    /// </summary>
    Task CancelRebuildAsync();

    /// <summary>
    /// 暂停重建
    /// </summary>
    Task PauseRebuildAsync();

    /// <summary>
    /// 恢复重建
    /// </summary>
    Task ResumeRebuildAsync();
}
