using Orleans;

namespace Curio.Orleans.Interfaces.Monitoring;

/// <summary>
/// 死信队列监控Grain接口
/// </summary>
public interface IDeadLetterMonitorGrain : IGrainWithStringKey
{
    /// <summary>
    /// 初始化监控
    /// </summary>
    Task InitializeAsync();

    /// <summary>
    /// 开始监控死信队列
    /// </summary>
    Task StartMonitoringAsync();

    /// <summary>
    /// 停止监控死信队列
    /// </summary>
    Task StopMonitoringAsync();

    /// <summary>
    /// 获取监控状态
    /// </summary>
    Task<MonitoringStatus> GetMonitoringStatusAsync();
}

/// <summary>
/// 监控状态
/// </summary>
[GenerateSerializer]
public record MonitoringStatus
{
    [Id(0)]
    public bool IsActive { get; init; }

    [Id(1)]
    public DateTime LastEventTime { get; init; }

    [Id(2)]
    public long ProcessedEventCount { get; init; }

    [Id(3)]
    public string? LastError { get; init; }
}
